hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.3':
    '@babel/compat-data': private
  '@babel/core@7.27.3':
    '@babel/core': private
  '@babel/generator@7.27.3':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.27.3)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.3)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.27.3)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.3':
    '@babel/helpers': private
  '@babel/parser@7.27.3':
    '@babel/parser': private
  '@babel/plugin-syntax-decorators@7.27.1(@babel/core@7.27.3)':
    '@babel/plugin-syntax-decorators': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.3)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.27.3)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.27.3)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-typescript@7.27.1(@babel/core@7.27.3)':
    '@babel/plugin-transform-typescript': private
  '@babel/preset-typescript@7.27.1(@babel/core@7.27.3)':
    '@babel/preset-typescript': private
  '@babel/runtime@7.27.3':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.3':
    '@babel/traverse': private
  '@babel/types@7.27.3':
    '@babel/types': private
  '@emotion/hash@0.9.2':
    '@emotion/hash': private
  '@esbuild/aix-ppc64@0.25.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.17.6':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.17.6':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.17.6':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.17.6':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.17.6':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.17.6':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.17.6':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.17.6':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.17.6':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.17.6':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.17.6':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.17.6':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.17.6':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.17.6':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.17.6':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.17.6':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.5':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.17.6':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.5':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.17.6':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.17.6':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.17.6':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.17.6':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.17.6':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.1':
    '@eslint/js': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@jspm/core@2.1.0':
    '@jspm/core': private
  '@mdx-js/mdx@2.3.0':
    '@mdx-js/mdx': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': private
  '@npmcli/fs@3.1.1':
    '@npmcli/fs': private
  '@npmcli/git@4.1.0':
    '@npmcli/git': private
  '@npmcli/package-json@4.0.1':
    '@npmcli/package-json': private
  '@npmcli/promise-spawn@6.0.2':
    '@npmcli/promise-spawn': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@remix-run/express@2.16.7(express@4.21.2)(typescript@5.8.3)':
    '@remix-run/express': private
  '@remix-run/router@1.23.0':
    '@remix-run/router': private
  '@remix-run/server-runtime@2.16.7(typescript@5.8.3)':
    '@remix-run/server-runtime': private
  '@remix-run/web-blob@3.1.0':
    '@remix-run/web-blob': private
  '@remix-run/web-fetch@4.4.2':
    '@remix-run/web-fetch': private
  '@remix-run/web-file@3.1.0':
    '@remix-run/web-file': private
  '@remix-run/web-form-data@3.1.0':
    '@remix-run/web-form-data': private
  '@remix-run/web-stream@1.1.0':
    '@remix-run/web-stream': private
  '@rollup/rollup-android-arm-eabi@4.41.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.41.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.41.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.41.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.41.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.41.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.41.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.41.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.41.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.41.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.41.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.41.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.41.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.41.1':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.41.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.41.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.41.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.41.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.41.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.41.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@types/acorn@4.0.6':
    '@types/acorn': private
  '@types/cookie@0.6.0':
    '@types/cookie': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/estree-jsx@1.0.5':
    '@types/estree-jsx': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/hast@2.3.10':
    '@types/hast': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/mdast@3.0.15':
    '@types/mdast': private
  '@types/mdx@2.0.13':
    '@types/mdx': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/node@22.15.24':
    '@types/node': private
  '@types/prop-types@15.7.14':
    '@types/prop-types': private
  '@types/semver@7.7.0':
    '@types/semver': private
  '@types/unist@2.0.11':
    '@types/unist': private
  '@typescript-eslint/scope-manager@6.21.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@6.21.0(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@6.21.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@6.21.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@6.21.0(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@6.21.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@unrs/resolver-binding-darwin-arm64@1.7.8':
    '@unrs/resolver-binding-darwin-arm64': private
  '@unrs/resolver-binding-darwin-x64@1.7.8':
    '@unrs/resolver-binding-darwin-x64': private
  '@unrs/resolver-binding-freebsd-x64@1.7.8':
    '@unrs/resolver-binding-freebsd-x64': private
  '@unrs/resolver-binding-linux-arm-gnueabihf@1.7.8':
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  '@unrs/resolver-binding-linux-arm-musleabihf@1.7.8':
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  '@unrs/resolver-binding-linux-arm64-gnu@1.7.8':
    '@unrs/resolver-binding-linux-arm64-gnu': private
  '@unrs/resolver-binding-linux-arm64-musl@1.7.8':
    '@unrs/resolver-binding-linux-arm64-musl': private
  '@unrs/resolver-binding-linux-ppc64-gnu@1.7.8':
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-gnu@1.7.8':
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-musl@1.7.8':
    '@unrs/resolver-binding-linux-riscv64-musl': private
  '@unrs/resolver-binding-linux-s390x-gnu@1.7.8':
    '@unrs/resolver-binding-linux-s390x-gnu': private
  '@unrs/resolver-binding-linux-x64-gnu@1.7.8':
    '@unrs/resolver-binding-linux-x64-gnu': private
  '@unrs/resolver-binding-linux-x64-musl@1.7.8':
    '@unrs/resolver-binding-linux-x64-musl': private
  '@unrs/resolver-binding-wasm32-wasi@1.7.8':
    '@unrs/resolver-binding-wasm32-wasi': private
  '@unrs/resolver-binding-win32-arm64-msvc@1.7.8':
    '@unrs/resolver-binding-win32-arm64-msvc': private
  '@unrs/resolver-binding-win32-ia32-msvc@1.7.8':
    '@unrs/resolver-binding-win32-ia32-msvc': private
  '@unrs/resolver-binding-win32-x64-msvc@1.7.8':
    '@unrs/resolver-binding-win32-x64-msvc': private
  '@vanilla-extract/babel-plugin-debug-ids@1.2.0':
    '@vanilla-extract/babel-plugin-debug-ids': private
  '@vanilla-extract/css@1.17.2':
    '@vanilla-extract/css': private
  '@vanilla-extract/integration@6.5.0(@types/node@22.15.24)':
    '@vanilla-extract/integration': private
  '@vanilla-extract/private@1.0.7':
    '@vanilla-extract/private': private
  '@web3-storage/multipart-parser@1.0.0':
    '@web3-storage/multipart-parser': private
  '@zxing/text-encoding@0.9.0':
    '@zxing/text-encoding': private
  abort-controller@3.0.0:
    abort-controller: private
  accepts@1.3.8:
    accepts: private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn@8.14.1:
    acorn: private
  aggregate-error@3.1.0:
    aggregate-error: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-flatten@1.1.1:
    array-flatten: private
  array-includes@3.1.8:
    array-includes: private
  array-union@2.1.0:
    array-union: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  astring@1.9.0:
    astring: private
  async-function@1.0.0:
    async-function: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.3:
    axe-core: private
  axobject-query@4.1.0:
    axobject-query: private
  bail@2.0.2:
    bail: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  basic-auth@2.0.1:
    basic-auth: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bl@4.1.0:
    bl: private
  body-parser@1.20.3:
    body-parser: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserify-zlib@0.1.4:
    browserify-zlib: private
  browserslist@4.25.0:
    browserslist: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@5.7.1:
    buffer: private
  bytes@3.1.2:
    bytes: private
  cac@6.7.14:
    cac: private
  cacache@17.1.4:
    cacache: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase-css@2.0.1:
    camelcase-css: private
  caniuse-lite@1.0.30001720:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  chalk@4.1.2:
    chalk: private
  character-entities-html4@2.1.0:
    character-entities-html4: private
  character-entities-legacy@3.0.0:
    character-entities-legacy: private
  character-entities@2.0.2:
    character-entities: private
  character-reference-invalid@2.0.1:
    character-reference-invalid: private
  chokidar@3.6.0:
    chokidar: private
  chownr@1.1.4:
    chownr: private
  clean-stack@2.2.0:
    clean-stack: private
  cli-cursor@3.1.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  clone@1.0.4:
    clone: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  comma-separated-tokens@2.0.3:
    comma-separated-tokens: private
  commander@4.1.1:
    commander: private
  compressible@2.0.18:
    compressible: private
  compression@1.8.0:
    compression: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.1.8:
    confbox: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-signature@1.2.2:
    cookie-signature: private
  cookie@0.7.2:
    cookie: private
  core-util-is@1.0.3:
    core-util-is: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-what@6.1.0:
    css-what: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-uri-to-buffer@3.0.1:
    data-uri-to-buffer: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.1:
    debug: private
  decode-named-character-reference@1.1.0:
    decode-named-character-reference: private
  dedent@1.6.0:
    dedent: private
  deep-is@0.1.4:
    deep-is: private
  deep-object-diff@1.1.9:
    deep-object-diff: private
  deepmerge@4.3.1:
    deepmerge: private
  defaults@1.0.4:
    defaults: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  depd@2.0.0:
    depd: private
  dequal@2.0.3:
    dequal: private
  destroy@1.2.0:
    destroy: private
  didyoumean@1.2.2:
    didyoumean: private
  diff@5.2.0:
    diff: private
  dir-glob@3.0.1:
    dir-glob: private
  dlv@1.1.3:
    dlv: private
  doctrine@2.1.0:
    doctrine: private
  dotenv@16.5.0:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexify@3.7.1:
    duplexify: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ee-first@1.1.1:
    ee-first: private
  electron-to-chromium@1.5.161:
    electron-to-chromium: private
  emoji-regex@9.2.2:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  end-of-stream@1.4.4:
    end-of-stream: private
  err-code@2.0.3:
    err-code: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esbuild-plugins-node-modules-polyfill@1.7.0(esbuild@0.17.6):
    esbuild-plugins-node-modules-polyfill: private
  esbuild@0.17.6:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: private
  eslint-module-utils@2.12.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.10.1)(eslint@8.57.1):
    eslint-module-utils: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  espree@9.6.1:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-util-attach-comments@2.1.1:
    estree-util-attach-comments: private
  estree-util-build-jsx@2.2.2:
    estree-util-build-jsx: private
  estree-util-is-identifier-name@2.1.0:
    estree-util-is-identifier-name: private
  estree-util-to-js@1.2.0:
    estree-util-to-js: private
  estree-util-value-to-estree@1.3.0:
    estree-util-value-to-estree: private
  estree-util-visit@1.2.1:
    estree-util-visit: private
  estree-walker@3.0.3:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  eval@0.1.8:
    eval: private
  event-target-shim@5.0.1:
    event-target-shim: private
  execa@5.1.1:
    execa: private
  exit-hook@2.2.1:
    exit-hook: private
  express@4.21.2:
    express: private
  exsolve@1.0.5:
    exsolve: private
  extend@3.0.2:
    extend: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fault@2.0.1:
    fault: private
  fdir@6.4.5(picomatch@4.0.2):
    fdir: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  format@0.2.2:
    format: private
  forwarded@0.2.0:
    forwarded: private
  fraction.js@4.3.7:
    fraction.js: private
  fresh@0.5.2:
    fresh: private
  fs-constants@1.0.0:
    fs-constants: private
  fs-extra@10.1.0:
    fs-extra: private
  fs-minipass@3.0.3:
    fs-minipass: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  generic-names@4.0.0:
    generic-names: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-port@5.1.1:
    get-port: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@6.0.1:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  globals@13.24.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@11.1.0:
    globby: private
  globrex@0.1.2:
    globrex: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  gunzip-maybe@1.4.2:
    gunzip-maybe: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hast-util-to-estree@2.3.3:
    hast-util-to-estree: private
  hast-util-whitespace@2.0.1:
    hast-util-whitespace: private
  hosted-git-info@6.1.3:
    hosted-git-info: private
  http-errors@2.0.0:
    http-errors: private
  human-signals@2.1.0:
    human-signals: private
  iconv-lite@0.4.24:
    iconv-lite: private
  icss-utils@5.1.0(postcss@8.5.4):
    icss-utils: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  inline-style-parser@0.1.1:
    inline-style-parser: private
  internal-slot@1.1.0:
    internal-slot: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-alphabetical@2.0.1:
    is-alphabetical: private
  is-alphanumerical@2.0.1:
    is-alphanumerical: private
  is-arguments@1.2.0:
    is-arguments: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-buffer@2.0.5:
    is-buffer: private
  is-bun-module@2.0.0:
    is-bun-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-decimal@2.0.1:
    is-decimal: private
  is-deflate@1.0.0:
    is-deflate: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-gzip@1.0.0:
    is-gzip: private
  is-hexadecimal@2.0.1:
    is-hexadecimal: private
  is-interactive@1.0.0:
    is-interactive: private
  is-map@2.0.3:
    is-map: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-obj@3.0.0:
    is-plain-obj: private
  is-reference@3.0.3:
    is-reference: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@2.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jackspeak@3.4.3:
    jackspeak: private
  javascript-stringify@2.1.0:
    javascript-stringify: private
  jiti@1.21.7:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.0.2:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@3.0.2:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  keyv@4.5.4:
    keyv: private
  kleur@4.1.5:
    kleur: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  loader-utils@3.3.1:
    loader-utils: private
  local-pkg@1.1.1:
    local-pkg: private
  locate-path@6.0.0:
    locate-path: private
  lodash.camelcase@4.3.0:
    lodash.camelcase: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash@4.17.21:
    lodash: private
  log-symbols@4.1.0:
    log-symbols: private
  longest-streak@3.1.0:
    longest-streak: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@7.18.3:
    lru-cache: private
  markdown-extensions@1.1.1:
    markdown-extensions: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdast-util-definitions@5.1.2:
    mdast-util-definitions: private
  mdast-util-from-markdown@1.3.1:
    mdast-util-from-markdown: private
  mdast-util-frontmatter@1.0.1:
    mdast-util-frontmatter: private
  mdast-util-mdx-expression@1.3.2:
    mdast-util-mdx-expression: private
  mdast-util-mdx-jsx@2.1.4:
    mdast-util-mdx-jsx: private
  mdast-util-mdx@2.0.1:
    mdast-util-mdx: private
  mdast-util-mdxjs-esm@1.3.1:
    mdast-util-mdxjs-esm: private
  mdast-util-phrasing@3.0.1:
    mdast-util-phrasing: private
  mdast-util-to-hast@12.3.0:
    mdast-util-to-hast: private
  mdast-util-to-markdown@1.5.0:
    mdast-util-to-markdown: private
  mdast-util-to-string@3.2.0:
    mdast-util-to-string: private
  media-query-parser@2.0.2:
    media-query-parser: private
  media-typer@0.3.0:
    media-typer: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micromark-core-commonmark@1.1.0:
    micromark-core-commonmark: private
  micromark-extension-frontmatter@1.1.1:
    micromark-extension-frontmatter: private
  micromark-extension-mdx-expression@1.0.8:
    micromark-extension-mdx-expression: private
  micromark-extension-mdx-jsx@1.0.5:
    micromark-extension-mdx-jsx: private
  micromark-extension-mdx-md@1.0.1:
    micromark-extension-mdx-md: private
  micromark-extension-mdxjs-esm@1.0.5:
    micromark-extension-mdxjs-esm: private
  micromark-extension-mdxjs@1.0.1:
    micromark-extension-mdxjs: private
  micromark-factory-destination@1.1.0:
    micromark-factory-destination: private
  micromark-factory-label@1.1.0:
    micromark-factory-label: private
  micromark-factory-mdx-expression@1.0.9:
    micromark-factory-mdx-expression: private
  micromark-factory-space@1.1.0:
    micromark-factory-space: private
  micromark-factory-title@1.1.0:
    micromark-factory-title: private
  micromark-factory-whitespace@1.1.0:
    micromark-factory-whitespace: private
  micromark-util-character@1.2.0:
    micromark-util-character: private
  micromark-util-chunked@1.1.0:
    micromark-util-chunked: private
  micromark-util-classify-character@1.1.0:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@1.1.0:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@1.1.0:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@1.1.0:
    micromark-util-decode-string: private
  micromark-util-encode@1.1.0:
    micromark-util-encode: private
  micromark-util-events-to-acorn@1.2.3:
    micromark-util-events-to-acorn: private
  micromark-util-html-tag-name@1.2.0:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@1.1.0:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@1.1.0:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@1.2.0:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@1.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@1.1.0:
    micromark-util-symbol: private
  micromark-util-types@1.1.0:
    micromark-util-types: private
  micromark@3.2.0:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  minimatch@9.0.5:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass-collect@1.0.2:
    minipass-collect: private
  minipass-flush@1.0.5:
    minipass-flush: private
  minipass-pipeline@1.2.4:
    minipass-pipeline: private
  minipass@7.1.2:
    minipass: private
  minizlib@2.1.2:
    minizlib: private
  mkdirp-classic@0.5.3:
    mkdirp-classic: private
  mkdirp@1.0.4:
    mkdirp: private
  mlly@1.7.4:
    mlly: private
  modern-ahocorasick@1.1.0:
    modern-ahocorasick: private
  morgan@1.10.0:
    morgan: private
  mri@1.2.0:
    mri: private
  mrmime@1.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  napi-postinstall@0.2.4:
    napi-postinstall: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@0.6.4:
    negotiator: private
  node-releases@2.0.19:
    node-releases: private
  normalize-package-data@5.0.0:
    normalize-package-data: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  npm-install-checks@6.3.0:
    npm-install-checks: private
  npm-normalize-package-bin@3.0.1:
    npm-normalize-package-bin: private
  npm-package-arg@10.1.0:
    npm-package-arg: private
  npm-pick-manifest@8.0.2:
    npm-pick-manifest: private
  npm-run-path@4.0.1:
    npm-run-path: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  on-finished@2.4.1:
    on-finished: private
  on-headers@1.0.2:
    on-headers: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  optionator@0.9.4:
    optionator: private
  ora@5.4.1:
    ora: private
  outdent@0.8.0:
    outdent: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@4.0.0:
    p-map: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  pako@0.2.9:
    pako: private
  parent-module@1.0.1:
    parent-module: private
  parse-entities@4.0.2:
    parse-entities: private
  parse-ms@2.1.0:
    parse-ms: private
  parseurl@1.3.3:
    parseurl: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  pathe@1.1.2:
    pathe: private
  peek-stream@1.1.3:
    peek-stream: private
  periscopic@3.1.0:
    periscopic: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pidtree@0.6.0:
    pidtree: private
  pify@2.3.0:
    pify: private
  pirates@4.0.7:
    pirates: private
  pkg-types@2.1.0:
    pkg-types: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-discard-duplicates@5.1.0(postcss@8.5.4):
    postcss-discard-duplicates: private
  postcss-import@15.1.0(postcss@8.5.4):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.4):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.5.4):
    postcss-load-config: private
  postcss-modules-extract-imports@3.1.0(postcss@8.5.4):
    postcss-modules-extract-imports: private
  postcss-modules-local-by-default@4.2.0(postcss@8.5.4):
    postcss-modules-local-by-default: private
  postcss-modules-scope@3.2.1(postcss@8.5.4):
    postcss-modules-scope: private
  postcss-modules-values@4.0.0(postcss@8.5.4):
    postcss-modules-values: private
  postcss-modules@6.0.1(postcss@8.5.4):
    postcss-modules: private
  postcss-nested@6.2.0(postcss@8.5.4):
    postcss-nested: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier@2.8.8:
    prettier: private
  pretty-ms@7.0.1:
    pretty-ms: private
  proc-log@3.0.0:
    proc-log: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  promise-inflight@1.0.1:
    promise-inflight: private
  promise-retry@2.0.1:
    promise-retry: private
  prop-types@15.8.1:
    prop-types: private
  property-information@6.5.0:
    property-information: private
  proxy-addr@2.0.7:
    proxy-addr: private
  pump@3.0.2:
    pump: private
  pumpify@1.5.1:
    pumpify: private
  punycode@2.3.1:
    punycode: private
  qs@6.13.0:
    qs: private
  quansync@0.2.10:
    quansync: private
  queue-microtask@1.2.3:
    queue-microtask: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  react-is@16.13.1:
    react-is: private
  react-refresh@0.14.2:
    react-refresh: private
  react-router-dom@6.30.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-router-dom: private
  react-router@6.30.0(react@18.3.1):
    react-router: private
  read-cache@1.0.0:
    read-cache: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  remark-frontmatter@4.0.1:
    remark-frontmatter: private
  remark-mdx-frontmatter@1.1.1:
    remark-mdx-frontmatter: private
  remark-mdx@2.3.0:
    remark-mdx: private
  remark-parse@10.0.2:
    remark-parse: private
  remark-rehype@10.1.0:
    remark-rehype: private
  require-like@0.1.2:
    require-like: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve.exports@2.0.3:
    resolve.exports: private
  resolve@2.0.0-next.5:
    resolve: private
  restore-cursor@3.1.0:
    restore-cursor: private
  retry@0.12.0:
    retry: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  rollup@4.41.1:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  sade@1.8.1:
    sade: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.23.2:
    scheduler: private
  semver@7.7.2:
    semver: private
  send@0.19.0:
    send: private
  serve-static@1.16.2:
    serve-static: private
  set-cookie-parser@2.7.1:
    set-cookie-parser: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  slash@3.0.0:
    slash: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.7.4:
    source-map: private
  space-separated-tokens@2.0.2:
    space-separated-tokens: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@3.0.1:
    spdx-expression-parse: private
  spdx-license-ids@3.0.21:
    spdx-license-ids: private
  ssri@10.0.6:
    ssri: private
  stable-hash@0.0.5:
    stable-hash: private
  statuses@2.0.1:
    statuses: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  stream-shift@1.0.3:
    stream-shift: private
  stream-slice@0.1.2:
    stream-slice: private
  string-hash@1.1.3:
    string-hash: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@5.1.2:
    string-width: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.1.1:
    string_decoder: private
  stringify-entities@4.0.4:
    stringify-entities: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  style-to-object@0.4.4:
    style-to-object: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  tar-fs@2.1.3:
    tar-fs: private
  tar-stream@2.2.0:
    tar-stream: private
  tar@6.2.1:
    tar: private
  text-table@0.2.0:
    text-table: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  through2@2.0.5:
    through2: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  toml@3.0.0:
    toml: private
  trim-lines@3.0.1:
    trim-lines: private
  trough@2.2.0:
    trough: private
  ts-api-utils@1.4.3(typescript@5.8.3):
    ts-api-utils: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  tsconfck@3.1.6(typescript@5.8.3):
    tsconfck: private
  tsconfig-paths@4.2.0:
    tsconfig-paths: private
  turbo-stream@2.4.1:
    turbo-stream: private
  type-check@0.4.0:
    type-check: private
  type-fest@0.20.2:
    type-fest: private
  type-is@1.6.18:
    type-is: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  ufo@1.6.1:
    ufo: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@6.21.0:
    undici-types: private
  undici@6.21.3:
    undici: private
  unified@10.1.2:
    unified: private
  unique-filename@3.0.0:
    unique-filename: private
  unique-slug@4.0.0:
    unique-slug: private
  unist-util-generated@2.0.1:
    unist-util-generated: private
  unist-util-is@5.2.1:
    unist-util-is: private
  unist-util-position-from-estree@1.1.2:
    unist-util-position-from-estree: private
  unist-util-position@4.0.4:
    unist-util-position: private
  unist-util-remove-position@4.0.2:
    unist-util-remove-position: private
  unist-util-stringify-position@3.0.3:
    unist-util-stringify-position: private
  unist-util-visit-parents@5.1.3:
    unist-util-visit-parents: private
  unist-util-visit@4.1.2:
    unist-util-visit: private
  universalify@2.0.1:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  unrs-resolver@1.7.8:
    unrs-resolver: private
  update-browserslist-db@1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  util-deprecate@1.0.2:
    util-deprecate: private
  util@0.12.5:
    util: private
  utils-merge@1.0.1:
    utils-merge: private
  uvu@0.5.6:
    uvu: private
  valibot@0.41.0(typescript@5.8.3):
    valibot: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  validate-npm-package-name@5.0.1:
    validate-npm-package-name: private
  vary@1.1.2:
    vary: private
  vfile-message@3.1.4:
    vfile-message: private
  vfile@5.3.7:
    vfile: private
  vite-node@3.1.4(@types/node@22.15.24)(jiti@1.21.7)(yaml@2.8.0):
    vite-node: private
  wcwidth@1.0.1:
    wcwidth: private
  web-encoding@1.1.5:
    web-encoding: private
  web-streams-polyfill@3.3.3:
    web-streams-polyfill: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  ws@7.5.10:
    ws: private
  xtend@4.0.2:
    xtend: private
  yallist@4.0.0:
    yallist: private
  yaml@2.8.0:
    yaml: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zwitch@2.0.4:
    zwitch: private
ignoredBuilds:
  - unrs-resolver
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Thu, 29 May 2025 20:17:13 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.4.3'
  - '@emnapi/runtime@1.4.3'
  - '@emnapi/wasi-threads@1.0.2'
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.17.6'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.17.6'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.17.6'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-x64@0.17.6'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.17.6'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.17.6'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.17.6'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.17.6'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.17.6'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.17.6'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.17.6'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.17.6'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.17.6'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.17.6'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.17.6'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.17.6'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.17.6'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.17.6'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.17.6'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.17.6'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@esbuild/win32-x64@0.17.6'
  - '@esbuild/win32-x64@0.21.5'
  - '@esbuild/win32-x64@0.25.5'
  - '@napi-rs/wasm-runtime@0.2.10'
  - '@rollup/rollup-android-arm-eabi@4.41.1'
  - '@rollup/rollup-android-arm64@4.41.1'
  - '@rollup/rollup-darwin-x64@4.41.1'
  - '@rollup/rollup-freebsd-arm64@4.41.1'
  - '@rollup/rollup-freebsd-x64@4.41.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.41.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.41.1'
  - '@rollup/rollup-linux-arm64-gnu@4.41.1'
  - '@rollup/rollup-linux-arm64-musl@4.41.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.41.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-musl@4.41.1'
  - '@rollup/rollup-linux-s390x-gnu@4.41.1'
  - '@rollup/rollup-linux-x64-gnu@4.41.1'
  - '@rollup/rollup-linux-x64-musl@4.41.1'
  - '@rollup/rollup-win32-arm64-msvc@4.41.1'
  - '@rollup/rollup-win32-ia32-msvc@4.41.1'
  - '@rollup/rollup-win32-x64-msvc@4.41.1'
  - '@tybys/wasm-util@0.9.0'
  - '@unrs/resolver-binding-darwin-x64@1.7.8'
  - '@unrs/resolver-binding-freebsd-x64@1.7.8'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.7.8'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.7.8'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.7.8'
  - '@unrs/resolver-binding-linux-arm64-musl@1.7.8'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.7.8'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.7.8'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.7.8'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.7.8'
  - '@unrs/resolver-binding-linux-x64-gnu@1.7.8'
  - '@unrs/resolver-binding-linux-x64-musl@1.7.8'
  - '@unrs/resolver-binding-wasm32-wasi@1.7.8'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.7.8'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.7.8'
  - '@unrs/resolver-binding-win32-x64-msvc@1.7.8'
  - tslib@2.8.1
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
