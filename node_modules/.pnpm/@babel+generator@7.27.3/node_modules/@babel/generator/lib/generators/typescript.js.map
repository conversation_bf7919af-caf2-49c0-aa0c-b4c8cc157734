{"version": 3, "names": ["TSTypeAnnotation", "node", "parent", "token", "type", "typeAnnotation", "space", "optional", "print", "TSTypeParameterInstantiation", "printTrailingSeparator", "params", "length", "tokenMap", "start", "end", "find", "t", "matchesOriginal", "shouldPrintTrailingComma", "printList", "TSTypeParameter", "const", "word", "in", "out", "name", "constraint", "default", "TSParameterProperty", "accessibility", "readonly", "_param", "parameter", "TSDeclareFunction", "declare", "_functionHead", "semicolon", "TSDeclareMethod", "_classMethodHead", "TSQualifiedName", "left", "right", "TSCallSignatureDeclaration", "tsPrintSignatureDeclarationBase", "maybePrintTrailingCommaOrSemicolon", "printer", "endMatches", "TSConstructSignatureDeclaration", "TSPropertySignature", "tsPrintPropertyOrMethodName", "computed", "key", "TSMethodSignature", "kind", "TSIndexSignature", "static", "isStatic", "_parameters", "parameters", "TSAnyKeyword", "TSBigIntKeyword", "TSUnknownKeyword", "TSNumberKeyword", "TSObjectKeyword", "TSBooleanKeyword", "TSStringKeyword", "TSSymbolKeyword", "TSVoidKeyword", "TSUndefinedKeyword", "TSNullKeyword", "TSNeverKeyword", "TSIntrinsicKeyword", "TSThisType", "TSFunctionType", "tsPrintFunctionOrConstructorType", "TSConstructorType", "abstract", "typeParameters", "returnType", "TSTypeReference", "typeArguments", "typeName", "TSTypePredicate", "asserts", "parameterName", "TSTypeQuery", "exprName", "TSTypeLiteral", "printBraced", "printJoin", "members", "TSArrayType", "elementType", "TSTupleType", "elementTypes", "TSOptionalType", "TSRestType", "TSNamedTupleMember", "label", "TSUnionType", "tsPrintUnionOrIntersectionType", "TSIntersectionType", "sep", "_printer$tokenMap", "hasLeadingToken", "startMatches", "types", "undefined", "i", "TSConditionalType", "checkType", "extendsType", "trueType", "falseType", "TSInferType", "typeParameter", "TSParenthesizedType", "TSTypeOperator", "operator", "TSIndexedAccessType", "objectType", "indexType", "TSMappedType", "nameType", "exit", "enterDelimited", "tokenIfPlusMinus", "self", "tok", "TSTemplateLiteralType", "_printTemplate", "TSLiteralType", "literal", "TSClassImplements", "expression", "TSInterfaceDeclaration", "id", "extends", "extendz", "body", "TSInterfaceBody", "TSTypeAliasDeclaration", "TSTypeExpression", "TSTypeAssertion", "TSInstantiationExpression", "TSEnumDeclaration", "isConst", "TSEnumBody", "call", "_this$shouldPrintTrai", "TSEnumMember", "initializer", "TSModuleDeclaration", "global", "TSModuleBlock", "printSequence", "TSImportType", "argument", "qualifier", "options", "TSImportEqualsDeclaration", "moduleReference", "isExport", "TSExternalModuleReference", "TSNonNullExpression", "TSExportAssignment", "TSNamespaceExportDeclaration", "tsPrintClassMemberModifiers", "isPrivateField", "isPublicField", "printModifiersList", "override", "cb", "rightBrace", "modifiers", "_printer$tokenMap2", "modifiersSet", "Set", "modifier", "add", "has", "value", "delete", "size"], "sources": ["../../src/generators/typescript.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport type * as t from \"@babel/types\";\n\nexport function TSTypeAnnotation(\n  this: Printer,\n  node: t.TSTypeAnnotation,\n  parent: t.Node,\n) {\n  // TODO(@nicolo-ribaudo): investigate not including => in the range\n  // of the return type of an arrow function type\n  this.token(\n    (parent.type === \"TSFunctionType\" || parent.type === \"TSConstructorType\") &&\n      (process.env.BABEL_8_BREAKING\n        ? // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST shape\n          parent.returnType\n        : // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST shape\n          parent.typeAnnotation) === node\n      ? \"=>\"\n      : \":\",\n  );\n  this.space();\n  // @ts-expect-error todo(flow->ts) can this be removed? `.optional` looks to be not existing property\n  if (node.optional) this.token(\"?\");\n  this.print(node.typeAnnotation);\n}\n\nexport function TSTypeParameterInstantiation(\n  this: Printer,\n  node: t.TSTypeParameterInstantiation,\n  parent: t.Node,\n): void {\n  this.token(\"<\");\n\n  let printTrailingSeparator =\n    parent.type === \"ArrowFunctionExpression\" && node.params.length === 1;\n  if (this.tokenMap && node.start != null && node.end != null) {\n    // Only force the trailing comma for pre-existing nodes if they\n    // already had a comma (either because they were multi-param, or\n    // because they had a trailing comma)\n    printTrailingSeparator &&= !!this.tokenMap.find(node, t =>\n      this.tokenMap.matchesOriginal(t, \",\"),\n    );\n    // Preserve the trailing comma if it was there before\n    printTrailingSeparator ||= this.shouldPrintTrailingComma(\">\");\n  }\n\n  this.printList(node.params, printTrailingSeparator);\n  this.token(\">\");\n}\n\nexport { TSTypeParameterInstantiation as TSTypeParameterDeclaration };\n\nexport function TSTypeParameter(this: Printer, node: t.TSTypeParameter) {\n  if (node.const) {\n    this.word(\"const\");\n    this.space();\n  }\n\n  if (node.in) {\n    this.word(\"in\");\n    this.space();\n  }\n\n  if (node.out) {\n    this.word(\"out\");\n    this.space();\n  }\n\n  this.word(\n    !process.env.BABEL_8_BREAKING\n      ? (node.name as unknown as string)\n      : (node.name as unknown as t.Identifier).name,\n  );\n\n  if (node.constraint) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.print(node.constraint);\n  }\n\n  if (node.default) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.default);\n  }\n}\n\nexport function TSParameterProperty(\n  this: Printer,\n  node: t.TSParameterProperty,\n) {\n  if (node.accessibility) {\n    this.word(node.accessibility);\n    this.space();\n  }\n\n  if (node.readonly) {\n    this.word(\"readonly\");\n    this.space();\n  }\n\n  this._param(node.parameter);\n}\n\nexport function TSDeclareFunction(\n  this: Printer,\n  node: t.TSDeclareFunction,\n  parent: t.ParentMaps[\"TSDeclareFunction\"],\n) {\n  if (node.declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this._functionHead(node, parent);\n  this.semicolon();\n}\n\nexport function TSDeclareMethod(this: Printer, node: t.TSDeclareMethod) {\n  this._classMethodHead(node);\n  this.semicolon();\n}\n\nexport function TSQualifiedName(this: Printer, node: t.TSQualifiedName) {\n  this.print(node.left);\n  this.token(\".\");\n  this.print(node.right);\n}\n\nexport function TSCallSignatureDeclaration(\n  this: Printer,\n  node: t.TSCallSignatureDeclaration,\n) {\n  this.tsPrintSignatureDeclarationBase(node);\n  maybePrintTrailingCommaOrSemicolon(this, node);\n}\n\nfunction maybePrintTrailingCommaOrSemicolon(printer: Printer, node: t.Node) {\n  if (!printer.tokenMap || !node.start || !node.end) {\n    printer.semicolon();\n    return;\n  }\n\n  if (printer.tokenMap.endMatches(node, \",\")) {\n    printer.token(\",\");\n  } else if (printer.tokenMap.endMatches(node, \";\")) {\n    printer.semicolon();\n  }\n}\n\nexport function TSConstructSignatureDeclaration(\n  this: Printer,\n  node: t.TSConstructSignatureDeclaration,\n) {\n  this.word(\"new\");\n  this.space();\n  this.tsPrintSignatureDeclarationBase(node);\n  maybePrintTrailingCommaOrSemicolon(this, node);\n}\n\nexport function TSPropertySignature(\n  this: Printer,\n  node: t.TSPropertySignature,\n) {\n  const { readonly } = node;\n  if (readonly) {\n    this.word(\"readonly\");\n    this.space();\n  }\n  this.tsPrintPropertyOrMethodName(node);\n  this.print(node.typeAnnotation);\n  maybePrintTrailingCommaOrSemicolon(this, node);\n}\n\nexport function tsPrintPropertyOrMethodName(\n  this: Printer,\n  node: t.TSPropertySignature | t.TSMethodSignature,\n) {\n  if (node.computed) {\n    this.token(\"[\");\n  }\n  this.print(node.key);\n  if (node.computed) {\n    this.token(\"]\");\n  }\n  if (node.optional) {\n    this.token(\"?\");\n  }\n}\n\nexport function TSMethodSignature(this: Printer, node: t.TSMethodSignature) {\n  const { kind } = node;\n  if (kind === \"set\" || kind === \"get\") {\n    this.word(kind);\n    this.space();\n  }\n  this.tsPrintPropertyOrMethodName(node);\n  this.tsPrintSignatureDeclarationBase(node);\n  maybePrintTrailingCommaOrSemicolon(this, node);\n}\n\nexport function TSIndexSignature(this: Printer, node: t.TSIndexSignature) {\n  const { readonly, static: isStatic } = node;\n  if (isStatic) {\n    this.word(\"static\");\n    this.space();\n  }\n  if (readonly) {\n    this.word(\"readonly\");\n    this.space();\n  }\n  this.token(\"[\");\n  this._parameters(node.parameters, \"]\");\n  this.print(node.typeAnnotation);\n  maybePrintTrailingCommaOrSemicolon(this, node);\n}\n\nexport function TSAnyKeyword(this: Printer) {\n  this.word(\"any\");\n}\nexport function TSBigIntKeyword(this: Printer) {\n  this.word(\"bigint\");\n}\nexport function TSUnknownKeyword(this: Printer) {\n  this.word(\"unknown\");\n}\nexport function TSNumberKeyword(this: Printer) {\n  this.word(\"number\");\n}\nexport function TSObjectKeyword(this: Printer) {\n  this.word(\"object\");\n}\nexport function TSBooleanKeyword(this: Printer) {\n  this.word(\"boolean\");\n}\nexport function TSStringKeyword(this: Printer) {\n  this.word(\"string\");\n}\nexport function TSSymbolKeyword(this: Printer) {\n  this.word(\"symbol\");\n}\nexport function TSVoidKeyword(this: Printer) {\n  this.word(\"void\");\n}\nexport function TSUndefinedKeyword(this: Printer) {\n  this.word(\"undefined\");\n}\nexport function TSNullKeyword(this: Printer) {\n  this.word(\"null\");\n}\nexport function TSNeverKeyword(this: Printer) {\n  this.word(\"never\");\n}\nexport function TSIntrinsicKeyword(this: Printer) {\n  this.word(\"intrinsic\");\n}\n\nexport function TSThisType(this: Printer) {\n  this.word(\"this\");\n}\n\nexport function TSFunctionType(this: Printer, node: t.TSFunctionType) {\n  this.tsPrintFunctionOrConstructorType(node);\n}\n\nexport function TSConstructorType(this: Printer, node: t.TSConstructorType) {\n  if (node.abstract) {\n    this.word(\"abstract\");\n    this.space();\n  }\n  this.word(\"new\");\n  this.space();\n  this.tsPrintFunctionOrConstructorType(node);\n}\n\nexport function tsPrintFunctionOrConstructorType(\n  this: Printer,\n  node: t.TSFunctionType | t.TSConstructorType,\n) {\n  const { typeParameters } = node;\n  const parameters = process.env.BABEL_8_BREAKING\n    ? // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST shape\n      node.params\n    : // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST shape\n      node.parameters;\n  this.print(typeParameters);\n  this.token(\"(\");\n  this._parameters(parameters, \")\");\n  this.space();\n  const returnType = process.env.BABEL_8_BREAKING\n    ? // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST shape\n      node.returnType\n    : // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST shape\n      node.typeAnnotation;\n  this.print(returnType);\n}\n\nexport function TSTypeReference(this: Printer, node: t.TSTypeReference) {\n  const typeArguments = process.env.BABEL_8_BREAKING\n    ? // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST shape\n      node.typeArguments\n    : // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST shape\n      node.typeParameters;\n  this.print(node.typeName, !!typeArguments);\n  this.print(typeArguments);\n}\n\nexport function TSTypePredicate(this: Printer, node: t.TSTypePredicate) {\n  if (node.asserts) {\n    this.word(\"asserts\");\n    this.space();\n  }\n  this.print(node.parameterName);\n  if (node.typeAnnotation) {\n    this.space();\n    this.word(\"is\");\n    this.space();\n    this.print(node.typeAnnotation.typeAnnotation);\n  }\n}\n\nexport function TSTypeQuery(this: Printer, node: t.TSTypeQuery) {\n  this.word(\"typeof\");\n  this.space();\n  this.print(node.exprName);\n\n  const typeArguments = process.env.BABEL_8_BREAKING\n    ? //@ts-ignore(Babel 7 vs Babel 8) Babel 8 AST\n      node.typeArguments\n    : //@ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n      node.typeParameters;\n  if (typeArguments) {\n    this.print(typeArguments);\n  }\n}\n\nexport function TSTypeLiteral(this: Printer, node: t.TSTypeLiteral) {\n  printBraced(this, node, () => this.printJoin(node.members, true, true));\n}\n\nexport function TSArrayType(this: Printer, node: t.TSArrayType) {\n  this.print(node.elementType, true);\n\n  this.token(\"[\");\n  this.token(\"]\");\n}\n\nexport function TSTupleType(this: Printer, node: t.TSTupleType) {\n  this.token(\"[\");\n  this.printList(node.elementTypes, this.shouldPrintTrailingComma(\"]\"));\n  this.token(\"]\");\n}\n\nexport function TSOptionalType(this: Printer, node: t.TSOptionalType) {\n  this.print(node.typeAnnotation);\n  this.token(\"?\");\n}\n\nexport function TSRestType(this: Printer, node: t.TSRestType) {\n  this.token(\"...\");\n  this.print(node.typeAnnotation);\n}\n\nexport function TSNamedTupleMember(this: Printer, node: t.TSNamedTupleMember) {\n  this.print(node.label);\n  if (node.optional) this.token(\"?\");\n  this.token(\":\");\n  this.space();\n  this.print(node.elementType);\n}\n\nexport function TSUnionType(this: Printer, node: t.TSUnionType) {\n  tsPrintUnionOrIntersectionType(this, node, \"|\");\n}\n\nexport function TSIntersectionType(this: Printer, node: t.TSIntersectionType) {\n  tsPrintUnionOrIntersectionType(this, node, \"&\");\n}\n\nfunction tsPrintUnionOrIntersectionType(\n  printer: Printer,\n  node: t.TSUnionType | t.TSIntersectionType,\n  sep: \"|\" | \"&\",\n) {\n  let hasLeadingToken = 0;\n  if (printer.tokenMap?.startMatches(node, sep)) {\n    hasLeadingToken = 1;\n    printer.token(sep);\n  }\n\n  printer.printJoin(node.types, undefined, undefined, function (i) {\n    this.space();\n    this.token(sep, null, i + hasLeadingToken);\n    this.space();\n  });\n}\n\nexport function TSConditionalType(this: Printer, node: t.TSConditionalType) {\n  this.print(node.checkType);\n  this.space();\n  this.word(\"extends\");\n  this.space();\n  this.print(node.extendsType);\n  this.space();\n  this.token(\"?\");\n  this.space();\n  this.print(node.trueType);\n  this.space();\n  this.token(\":\");\n  this.space();\n  this.print(node.falseType);\n}\n\nexport function TSInferType(this: Printer, node: t.TSInferType) {\n  this.word(\"infer\");\n  this.print(node.typeParameter);\n}\n\nexport function TSParenthesizedType(\n  this: Printer,\n  node: t.TSParenthesizedType,\n) {\n  this.token(\"(\");\n  this.print(node.typeAnnotation);\n  this.token(\")\");\n}\n\nexport function TSTypeOperator(this: Printer, node: t.TSTypeOperator) {\n  this.word(node.operator);\n  this.space();\n  this.print(node.typeAnnotation);\n}\n\nexport function TSIndexedAccessType(\n  this: Printer,\n  node: t.TSIndexedAccessType,\n) {\n  this.print(node.objectType, true);\n  this.token(\"[\");\n  this.print(node.indexType);\n  this.token(\"]\");\n}\n\nexport function TSMappedType(this: Printer, node: t.TSMappedType) {\n  const { nameType, optional, readonly, typeAnnotation } = node;\n  this.token(\"{\");\n  const exit = this.enterDelimited();\n  this.space();\n  if (readonly) {\n    tokenIfPlusMinus(this, readonly);\n    this.word(\"readonly\");\n    this.space();\n  }\n\n  this.token(\"[\");\n  if (process.env.BABEL_8_BREAKING) {\n    // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST shape\n    this.word(node.key.name);\n  } else {\n    // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST shape\n    this.word(node.typeParameter.name);\n  }\n\n  this.space();\n  this.word(\"in\");\n  this.space();\n  if (process.env.BABEL_8_BREAKING) {\n    // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST shape\n    this.print(node.constraint);\n  } else {\n    // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST shape\n    this.print(node.typeParameter.constraint);\n  }\n\n  if (nameType) {\n    this.space();\n    this.word(\"as\");\n    this.space();\n    this.print(nameType);\n  }\n\n  this.token(\"]\");\n\n  if (optional) {\n    tokenIfPlusMinus(this, optional);\n    this.token(\"?\");\n  }\n\n  if (typeAnnotation) {\n    this.token(\":\");\n    this.space();\n    this.print(typeAnnotation);\n  }\n  this.space();\n  exit();\n  this.token(\"}\");\n}\n\nfunction tokenIfPlusMinus(self: Printer, tok: true | \"+\" | \"-\") {\n  if (tok !== true) {\n    self.token(tok);\n  }\n}\n\nexport function TSTemplateLiteralType(\n  this: Printer,\n  node: t.TSTemplateLiteralType,\n) {\n  this._printTemplate(node, node.types);\n}\n\nexport function TSLiteralType(this: Printer, node: t.TSLiteralType) {\n  this.print(node.literal);\n}\n\nexport function TSClassImplements(\n  this: Printer,\n  // TODO(Babel 8): Just use t.TSClassImplements\n  node: t.Node & {\n    expression: t.TSEntityName;\n    typeArguments?: t.TSTypeParameterInstantiation;\n  },\n) {\n  this.print(node.expression);\n  this.print(node.typeArguments);\n}\n\nexport { TSClassImplements as TSInterfaceHeritage };\n\nexport function TSInterfaceDeclaration(\n  this: Printer,\n  node: t.TSInterfaceDeclaration,\n) {\n  const { declare, id, typeParameters, extends: extendz, body } = node;\n  if (declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.word(\"interface\");\n  this.space();\n  this.print(id);\n  this.print(typeParameters);\n  if (extendz?.length) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.printList(extendz);\n  }\n  this.space();\n  this.print(body);\n}\n\nexport function TSInterfaceBody(this: Printer, node: t.TSInterfaceBody) {\n  printBraced(this, node, () => this.printJoin(node.body, true, true));\n}\n\nexport function TSTypeAliasDeclaration(\n  this: Printer,\n  node: t.TSTypeAliasDeclaration,\n) {\n  const { declare, id, typeParameters, typeAnnotation } = node;\n  if (declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.word(\"type\");\n  this.space();\n  this.print(id);\n  this.print(typeParameters);\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(typeAnnotation);\n  this.semicolon();\n}\n\nfunction TSTypeExpression(\n  this: Printer,\n  node: t.TSAsExpression | t.TSSatisfiesExpression,\n) {\n  const { type, expression, typeAnnotation } = node;\n  this.print(expression, true);\n  this.space();\n  this.word(type === \"TSAsExpression\" ? \"as\" : \"satisfies\");\n  this.space();\n  this.print(typeAnnotation);\n}\n\nexport {\n  TSTypeExpression as TSAsExpression,\n  TSTypeExpression as TSSatisfiesExpression,\n};\n\nexport function TSTypeAssertion(this: Printer, node: t.TSTypeAssertion) {\n  const { typeAnnotation, expression } = node;\n  this.token(\"<\");\n  this.print(typeAnnotation);\n  this.token(\">\");\n  this.space();\n  this.print(expression);\n}\n\nexport function TSInstantiationExpression(\n  this: Printer,\n  node: t.TSInstantiationExpression,\n) {\n  this.print(node.expression);\n  if (process.env.BABEL_8_BREAKING) {\n    // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST\n    this.print(node.typeArguments);\n  } else {\n    // @ts-ignore(Babel 7 vs Babel 8) Removed in Babel 8\n    this.print(node.typeParameters);\n  }\n}\n\nexport function TSEnumDeclaration(this: Printer, node: t.TSEnumDeclaration) {\n  const { declare, const: isConst, id } = node;\n  if (declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  if (isConst) {\n    this.word(\"const\");\n    this.space();\n  }\n  this.word(\"enum\");\n  this.space();\n  this.print(id);\n  this.space();\n\n  if (process.env.BABEL_8_BREAKING) {\n    // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST\n    this.print(node.body);\n  } else {\n    // cast to TSEnumBody for Babel 7 AST\n    TSEnumBody.call(this, node as unknown as t.TSEnumBody);\n  }\n}\n\nexport function TSEnumBody(this: Printer, node: t.TSEnumBody) {\n  printBraced(this, node, () =>\n    this.printList(\n      node.members,\n      this.shouldPrintTrailingComma(\"}\") ??\n        (process.env.BABEL_8_BREAKING ? false : true),\n      true,\n      true,\n    ),\n  );\n}\n\nexport function TSEnumMember(this: Printer, node: t.TSEnumMember) {\n  const { id, initializer } = node;\n  this.print(id);\n  if (initializer) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(initializer);\n  }\n}\n\nexport function TSModuleDeclaration(\n  this: Printer,\n  node: t.TSModuleDeclaration,\n) {\n  const { declare, id, kind } = node;\n\n  if (declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n\n  if (process.env.BABEL_8_BREAKING) {\n    if (kind !== \"global\") {\n      this.word(kind);\n      this.space();\n    }\n\n    this.print(node.id);\n    if (!node.body) {\n      this.semicolon();\n      return;\n    }\n    this.space();\n    this.print(node.body);\n  } else {\n    // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST shape\n    if (!node.global) {\n      this.word(kind ?? (id.type === \"Identifier\" ? \"namespace\" : \"module\"));\n      this.space();\n    }\n\n    this.print(id);\n\n    if (!node.body) {\n      this.semicolon();\n      return;\n    }\n\n    let body = node.body;\n    // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST shape\n    while (body.type === \"TSModuleDeclaration\") {\n      this.token(\".\");\n      // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST shape\n      this.print(body.id);\n      // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST shape\n      body = body.body;\n    }\n\n    this.space();\n    this.print(body);\n  }\n}\n\nexport function TSModuleBlock(this: Printer, node: t.TSModuleBlock) {\n  printBraced(this, node, () => this.printSequence(node.body, true));\n}\n\nexport function TSImportType(this: Printer, node: t.TSImportType) {\n  const { argument, qualifier, options } = node;\n  this.word(\"import\");\n  this.token(\"(\");\n  this.print(argument);\n  if (options) {\n    this.token(\",\");\n    this.print(options);\n  }\n  this.token(\")\");\n  if (qualifier) {\n    this.token(\".\");\n    this.print(qualifier);\n  }\n  const typeArguments = process.env.BABEL_8_BREAKING\n    ? //@ts-ignore(Babel 7 vs Babel 8) Babel 8 AST\n      node.typeArguments\n    : //@ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n      node.typeParameters;\n  if (typeArguments) {\n    this.print(typeArguments);\n  }\n}\n\nexport function TSImportEqualsDeclaration(\n  this: Printer,\n  node: t.TSImportEqualsDeclaration,\n) {\n  const { id, moduleReference } = node;\n  if (\n    !process.env.BABEL_8_BREAKING &&\n    // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST\n    node.isExport\n  ) {\n    this.word(\"export\");\n    this.space();\n  }\n  this.word(\"import\");\n  this.space();\n  this.print(id);\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(moduleReference);\n  this.semicolon();\n}\n\nexport function TSExternalModuleReference(\n  this: Printer,\n  node: t.TSExternalModuleReference,\n) {\n  this.token(\"require(\");\n  this.print(node.expression);\n  this.token(\")\");\n}\n\nexport function TSNonNullExpression(\n  this: Printer,\n  node: t.TSNonNullExpression,\n) {\n  this.print(node.expression);\n  this.token(\"!\");\n}\n\nexport function TSExportAssignment(this: Printer, node: t.TSExportAssignment) {\n  this.word(\"export\");\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(node.expression);\n  this.semicolon();\n}\n\nexport function TSNamespaceExportDeclaration(\n  this: Printer,\n  node: t.TSNamespaceExportDeclaration,\n) {\n  this.word(\"export\");\n  this.space();\n  this.word(\"as\");\n  this.space();\n  this.word(\"namespace\");\n  this.space();\n  this.print(node.id);\n  this.semicolon();\n}\n\nexport function tsPrintSignatureDeclarationBase(this: Printer, node: any) {\n  const { typeParameters } = node;\n  const parameters = process.env.BABEL_8_BREAKING\n    ? node.params\n    : node.parameters;\n  this.print(typeParameters);\n  this.token(\"(\");\n  this._parameters(parameters, \")\");\n  const returnType = process.env.BABEL_8_BREAKING\n    ? node.returnType\n    : node.typeAnnotation;\n  this.print(returnType);\n}\n\nexport function tsPrintClassMemberModifiers(\n  this: Printer,\n  node:\n    | t.ClassProperty\n    | t.ClassAccessorProperty\n    | t.ClassPrivateProperty\n    | t.ClassMethod\n    | t.ClassPrivateMethod\n    | t.TSDeclareMethod,\n) {\n  const isPrivateField = node.type === \"ClassPrivateProperty\";\n  const isPublicField =\n    node.type === \"ClassAccessorProperty\" || node.type === \"ClassProperty\";\n  printModifiersList(this, node, [\n    isPublicField && node.declare && \"declare\",\n    !isPrivateField && node.accessibility,\n  ]);\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  printModifiersList(this, node, [\n    !isPrivateField && node.abstract && \"abstract\",\n    !isPrivateField && node.override && \"override\",\n    (isPublicField || isPrivateField) && node.readonly && \"readonly\",\n  ]);\n}\n\nfunction printBraced(printer: Printer, node: t.Node, cb: () => void) {\n  printer.token(\"{\");\n  const exit = printer.enterDelimited();\n  cb();\n  exit();\n  printer.rightBrace(node);\n}\n\nfunction printModifiersList(\n  printer: Printer,\n  node: t.Node,\n  modifiers: (string | false | null)[],\n) {\n  const modifiersSet = new Set<string>();\n  for (const modifier of modifiers) {\n    if (modifier) modifiersSet.add(modifier);\n  }\n\n  printer.tokenMap?.find(node, tok => {\n    if (modifiersSet.has(tok.value)) {\n      printer.token(tok.value);\n      printer.space();\n      modifiersSet.delete(tok.value);\n      return modifiersSet.size === 0;\n    }\n  });\n\n  for (const modifier of modifiersSet) {\n    printer.word(modifier);\n    printer.space();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,SAASA,gBAAgBA,CAE9BC,IAAwB,EACxBC,MAAc,EACd;EAGA,IAAI,CAACC,KAAK,CACR,CAACD,MAAM,CAACE,IAAI,KAAK,gBAAgB,IAAIF,MAAM,CAACE,IAAI,KAAK,mBAAmB,KAKlEF,MAAM,CAACG,cAAc,KAAMJ,IAAI,GACjC,IAAI,GACJ,GACN,CAAC;EACD,IAAI,CAACK,KAAK,CAAC,CAAC;EAEZ,IAAIL,IAAI,CAACM,QAAQ,EAAE,IAAI,CAACJ,SAAK,GAAI,CAAC;EAClC,IAAI,CAACK,KAAK,CAACP,IAAI,CAACI,cAAc,CAAC;AACjC;AAEO,SAASI,4BAA4BA,CAE1CR,IAAoC,EACpCC,MAAc,EACR;EACN,IAAI,CAACC,SAAK,GAAI,CAAC;EAEf,IAAIO,sBAAsB,GACxBR,MAAM,CAACE,IAAI,KAAK,yBAAyB,IAAIH,IAAI,CAACU,MAAM,CAACC,MAAM,KAAK,CAAC;EACvE,IAAI,IAAI,CAACC,QAAQ,IAAIZ,IAAI,CAACa,KAAK,IAAI,IAAI,IAAIb,IAAI,CAACc,GAAG,IAAI,IAAI,EAAE;IAI3DL,sBAAsB,KAAtBA,sBAAsB,GAAK,CAAC,CAAC,IAAI,CAACG,QAAQ,CAACG,IAAI,CAACf,IAAI,EAAEgB,CAAC,IACrD,IAAI,CAACJ,QAAQ,CAACK,eAAe,CAACD,CAAC,EAAE,GAAG,CACtC,CAAC;IAEDP,sBAAsB,KAAtBA,sBAAsB,GAAK,IAAI,CAACS,wBAAwB,CAAC,GAAG,CAAC;EAC/D;EAEA,IAAI,CAACC,SAAS,CAACnB,IAAI,CAACU,MAAM,EAAED,sBAAsB,CAAC;EACnD,IAAI,CAACP,SAAK,GAAI,CAAC;AACjB;AAIO,SAASkB,eAAeA,CAAgBpB,IAAuB,EAAE;EACtE,IAAIA,IAAI,CAACqB,KAAK,EAAE;IACd,IAAI,CAACC,IAAI,CAAC,OAAO,CAAC;IAClB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACd;EAEA,IAAIL,IAAI,CAACuB,EAAE,EAAE;IACX,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;IACf,IAAI,CAACjB,KAAK,CAAC,CAAC;EACd;EAEA,IAAIL,IAAI,CAACwB,GAAG,EAAE;IACZ,IAAI,CAACF,IAAI,CAAC,KAAK,CAAC;IAChB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACd;EAEA,IAAI,CAACiB,IAAI,CAEFtB,IAAI,CAACyB,IAEZ,CAAC;EAED,IAAIzB,IAAI,CAAC0B,UAAU,EAAE;IACnB,IAAI,CAACrB,KAAK,CAAC,CAAC;IACZ,IAAI,CAACiB,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACjB,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACP,IAAI,CAAC0B,UAAU,CAAC;EAC7B;EAEA,IAAI1B,IAAI,CAAC2B,OAAO,EAAE;IAChB,IAAI,CAACtB,KAAK,CAAC,CAAC;IACZ,IAAI,CAACH,SAAK,GAAI,CAAC;IACf,IAAI,CAACG,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACP,IAAI,CAAC2B,OAAO,CAAC;EAC1B;AACF;AAEO,SAASC,mBAAmBA,CAEjC5B,IAA2B,EAC3B;EACA,IAAIA,IAAI,CAAC6B,aAAa,EAAE;IACtB,IAAI,CAACP,IAAI,CAACtB,IAAI,CAAC6B,aAAa,CAAC;IAC7B,IAAI,CAACxB,KAAK,CAAC,CAAC;EACd;EAEA,IAAIL,IAAI,CAAC8B,QAAQ,EAAE;IACjB,IAAI,CAACR,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACd;EAEA,IAAI,CAAC0B,MAAM,CAAC/B,IAAI,CAACgC,SAAS,CAAC;AAC7B;AAEO,SAASC,iBAAiBA,CAE/BjC,IAAyB,EACzBC,MAAyC,EACzC;EACA,IAAID,IAAI,CAACkC,OAAO,EAAE;IAChB,IAAI,CAACZ,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAAC8B,aAAa,CAACnC,IAAI,EAAEC,MAAM,CAAC;EAChC,IAAI,CAACmC,SAAS,CAAC,CAAC;AAClB;AAEO,SAASC,eAAeA,CAAgBrC,IAAuB,EAAE;EACtE,IAAI,CAACsC,gBAAgB,CAACtC,IAAI,CAAC;EAC3B,IAAI,CAACoC,SAAS,CAAC,CAAC;AAClB;AAEO,SAASG,eAAeA,CAAgBvC,IAAuB,EAAE;EACtE,IAAI,CAACO,KAAK,CAACP,IAAI,CAACwC,IAAI,CAAC;EACrB,IAAI,CAACtC,SAAK,GAAI,CAAC;EACf,IAAI,CAACK,KAAK,CAACP,IAAI,CAACyC,KAAK,CAAC;AACxB;AAEO,SAASC,0BAA0BA,CAExC1C,IAAkC,EAClC;EACA,IAAI,CAAC2C,+BAA+B,CAAC3C,IAAI,CAAC;EAC1C4C,kCAAkC,CAAC,IAAI,EAAE5C,IAAI,CAAC;AAChD;AAEA,SAAS4C,kCAAkCA,CAACC,OAAgB,EAAE7C,IAAY,EAAE;EAC1E,IAAI,CAAC6C,OAAO,CAACjC,QAAQ,IAAI,CAACZ,IAAI,CAACa,KAAK,IAAI,CAACb,IAAI,CAACc,GAAG,EAAE;IACjD+B,OAAO,CAACT,SAAS,CAAC,CAAC;IACnB;EACF;EAEA,IAAIS,OAAO,CAACjC,QAAQ,CAACkC,UAAU,CAAC9C,IAAI,EAAE,GAAG,CAAC,EAAE;IAC1C6C,OAAO,CAAC3C,KAAK,CAAC,GAAG,CAAC;EACpB,CAAC,MAAM,IAAI2C,OAAO,CAACjC,QAAQ,CAACkC,UAAU,CAAC9C,IAAI,EAAE,GAAG,CAAC,EAAE;IACjD6C,OAAO,CAACT,SAAS,CAAC,CAAC;EACrB;AACF;AAEO,SAASW,+BAA+BA,CAE7C/C,IAAuC,EACvC;EACA,IAAI,CAACsB,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACsC,+BAA+B,CAAC3C,IAAI,CAAC;EAC1C4C,kCAAkC,CAAC,IAAI,EAAE5C,IAAI,CAAC;AAChD;AAEO,SAASgD,mBAAmBA,CAEjChD,IAA2B,EAC3B;EACA,MAAM;IAAE8B;EAAS,CAAC,GAAG9B,IAAI;EACzB,IAAI8B,QAAQ,EAAE;IACZ,IAAI,CAACR,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAAC4C,2BAA2B,CAACjD,IAAI,CAAC;EACtC,IAAI,CAACO,KAAK,CAACP,IAAI,CAACI,cAAc,CAAC;EAC/BwC,kCAAkC,CAAC,IAAI,EAAE5C,IAAI,CAAC;AAChD;AAEO,SAASiD,2BAA2BA,CAEzCjD,IAAiD,EACjD;EACA,IAAIA,IAAI,CAACkD,QAAQ,EAAE;IACjB,IAAI,CAAChD,SAAK,GAAI,CAAC;EACjB;EACA,IAAI,CAACK,KAAK,CAACP,IAAI,CAACmD,GAAG,CAAC;EACpB,IAAInD,IAAI,CAACkD,QAAQ,EAAE;IACjB,IAAI,CAAChD,SAAK,GAAI,CAAC;EACjB;EACA,IAAIF,IAAI,CAACM,QAAQ,EAAE;IACjB,IAAI,CAACJ,SAAK,GAAI,CAAC;EACjB;AACF;AAEO,SAASkD,iBAAiBA,CAAgBpD,IAAyB,EAAE;EAC1E,MAAM;IAAEqD;EAAK,CAAC,GAAGrD,IAAI;EACrB,IAAIqD,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,EAAE;IACpC,IAAI,CAAC/B,IAAI,CAAC+B,IAAI,CAAC;IACf,IAAI,CAAChD,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAAC4C,2BAA2B,CAACjD,IAAI,CAAC;EACtC,IAAI,CAAC2C,+BAA+B,CAAC3C,IAAI,CAAC;EAC1C4C,kCAAkC,CAAC,IAAI,EAAE5C,IAAI,CAAC;AAChD;AAEO,SAASsD,gBAAgBA,CAAgBtD,IAAwB,EAAE;EACxE,MAAM;IAAE8B,QAAQ;IAAEyB,MAAM,EAAEC;EAAS,CAAC,GAAGxD,IAAI;EAC3C,IAAIwD,QAAQ,EAAE;IACZ,IAAI,CAAClC,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACd;EACA,IAAIyB,QAAQ,EAAE;IACZ,IAAI,CAACR,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACH,SAAK,GAAI,CAAC;EACf,IAAI,CAACuD,WAAW,CAACzD,IAAI,CAAC0D,UAAU,EAAE,GAAG,CAAC;EACtC,IAAI,CAACnD,KAAK,CAACP,IAAI,CAACI,cAAc,CAAC;EAC/BwC,kCAAkC,CAAC,IAAI,EAAE5C,IAAI,CAAC;AAChD;AAEO,SAAS2D,YAAYA,CAAA,EAAgB;EAC1C,IAAI,CAACrC,IAAI,CAAC,KAAK,CAAC;AAClB;AACO,SAASsC,eAAeA,CAAA,EAAgB;EAC7C,IAAI,CAACtC,IAAI,CAAC,QAAQ,CAAC;AACrB;AACO,SAASuC,gBAAgBA,CAAA,EAAgB;EAC9C,IAAI,CAACvC,IAAI,CAAC,SAAS,CAAC;AACtB;AACO,SAASwC,eAAeA,CAAA,EAAgB;EAC7C,IAAI,CAACxC,IAAI,CAAC,QAAQ,CAAC;AACrB;AACO,SAASyC,eAAeA,CAAA,EAAgB;EAC7C,IAAI,CAACzC,IAAI,CAAC,QAAQ,CAAC;AACrB;AACO,SAAS0C,gBAAgBA,CAAA,EAAgB;EAC9C,IAAI,CAAC1C,IAAI,CAAC,SAAS,CAAC;AACtB;AACO,SAAS2C,eAAeA,CAAA,EAAgB;EAC7C,IAAI,CAAC3C,IAAI,CAAC,QAAQ,CAAC;AACrB;AACO,SAAS4C,eAAeA,CAAA,EAAgB;EAC7C,IAAI,CAAC5C,IAAI,CAAC,QAAQ,CAAC;AACrB;AACO,SAAS6C,aAAaA,CAAA,EAAgB;EAC3C,IAAI,CAAC7C,IAAI,CAAC,MAAM,CAAC;AACnB;AACO,SAAS8C,kBAAkBA,CAAA,EAAgB;EAChD,IAAI,CAAC9C,IAAI,CAAC,WAAW,CAAC;AACxB;AACO,SAAS+C,aAAaA,CAAA,EAAgB;EAC3C,IAAI,CAAC/C,IAAI,CAAC,MAAM,CAAC;AACnB;AACO,SAASgD,cAAcA,CAAA,EAAgB;EAC5C,IAAI,CAAChD,IAAI,CAAC,OAAO,CAAC;AACpB;AACO,SAASiD,kBAAkBA,CAAA,EAAgB;EAChD,IAAI,CAACjD,IAAI,CAAC,WAAW,CAAC;AACxB;AAEO,SAASkD,UAAUA,CAAA,EAAgB;EACxC,IAAI,CAAClD,IAAI,CAAC,MAAM,CAAC;AACnB;AAEO,SAASmD,cAAcA,CAAgBzE,IAAsB,EAAE;EACpE,IAAI,CAAC0E,gCAAgC,CAAC1E,IAAI,CAAC;AAC7C;AAEO,SAAS2E,iBAAiBA,CAAgB3E,IAAyB,EAAE;EAC1E,IAAIA,IAAI,CAAC4E,QAAQ,EAAE;IACjB,IAAI,CAACtD,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACiB,IAAI,CAAC,KAAK,CAAC;EAChB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACqE,gCAAgC,CAAC1E,IAAI,CAAC;AAC7C;AAEO,SAAS0E,gCAAgCA,CAE9C1E,IAA4C,EAC5C;EACA,MAAM;IAAE6E;EAAe,CAAC,GAAG7E,IAAI;EAC/B,MAAM0D,UAAU,GAIZ1D,IAAI,CAAC0D,UAAU;EACnB,IAAI,CAACnD,KAAK,CAACsE,cAAc,CAAC;EAC1B,IAAI,CAAC3E,SAAK,GAAI,CAAC;EACf,IAAI,CAACuD,WAAW,CAACC,UAAU,EAAE,GAAG,CAAC;EACjC,IAAI,CAACrD,KAAK,CAAC,CAAC;EACZ,MAAMyE,UAAU,GAIZ9E,IAAI,CAACI,cAAc;EACvB,IAAI,CAACG,KAAK,CAACuE,UAAU,CAAC;AACxB;AAEO,SAASC,eAAeA,CAAgB/E,IAAuB,EAAE;EACtE,MAAMgF,aAAa,GAIfhF,IAAI,CAAC6E,cAAc;EACvB,IAAI,CAACtE,KAAK,CAACP,IAAI,CAACiF,QAAQ,EAAE,CAAC,CAACD,aAAa,CAAC;EAC1C,IAAI,CAACzE,KAAK,CAACyE,aAAa,CAAC;AAC3B;AAEO,SAASE,eAAeA,CAAgBlF,IAAuB,EAAE;EACtE,IAAIA,IAAI,CAACmF,OAAO,EAAE;IAChB,IAAI,CAAC7D,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACE,KAAK,CAACP,IAAI,CAACoF,aAAa,CAAC;EAC9B,IAAIpF,IAAI,CAACI,cAAc,EAAE;IACvB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACiB,IAAI,CAAC,IAAI,CAAC;IACf,IAAI,CAACjB,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACP,IAAI,CAACI,cAAc,CAACA,cAAc,CAAC;EAChD;AACF;AAEO,SAASiF,WAAWA,CAAgBrF,IAAmB,EAAE;EAC9D,IAAI,CAACsB,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACP,IAAI,CAACsF,QAAQ,CAAC;EAEzB,MAAMN,aAAa,GAIfhF,IAAI,CAAC6E,cAAc;EACvB,IAAIG,aAAa,EAAE;IACjB,IAAI,CAACzE,KAAK,CAACyE,aAAa,CAAC;EAC3B;AACF;AAEO,SAASO,aAAaA,CAAgBvF,IAAqB,EAAE;EAClEwF,WAAW,CAAC,IAAI,EAAExF,IAAI,EAAE,MAAM,IAAI,CAACyF,SAAS,CAACzF,IAAI,CAAC0F,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACzE;AAEO,SAASC,WAAWA,CAAgB3F,IAAmB,EAAE;EAC9D,IAAI,CAACO,KAAK,CAACP,IAAI,CAAC4F,WAAW,EAAE,IAAI,CAAC;EAElC,IAAI,CAAC1F,SAAK,GAAI,CAAC;EACf,IAAI,CAACA,SAAK,GAAI,CAAC;AACjB;AAEO,SAAS2F,WAAWA,CAAgB7F,IAAmB,EAAE;EAC9D,IAAI,CAACE,SAAK,GAAI,CAAC;EACf,IAAI,CAACiB,SAAS,CAACnB,IAAI,CAAC8F,YAAY,EAAE,IAAI,CAAC5E,wBAAwB,CAAC,GAAG,CAAC,CAAC;EACrE,IAAI,CAAChB,SAAK,GAAI,CAAC;AACjB;AAEO,SAAS6F,cAAcA,CAAgB/F,IAAsB,EAAE;EACpE,IAAI,CAACO,KAAK,CAACP,IAAI,CAACI,cAAc,CAAC;EAC/B,IAAI,CAACF,SAAK,GAAI,CAAC;AACjB;AAEO,SAAS8F,UAAUA,CAAgBhG,IAAkB,EAAE;EAC5D,IAAI,CAACE,KAAK,CAAC,KAAK,CAAC;EACjB,IAAI,CAACK,KAAK,CAACP,IAAI,CAACI,cAAc,CAAC;AACjC;AAEO,SAAS6F,kBAAkBA,CAAgBjG,IAA0B,EAAE;EAC5E,IAAI,CAACO,KAAK,CAACP,IAAI,CAACkG,KAAK,CAAC;EACtB,IAAIlG,IAAI,CAACM,QAAQ,EAAE,IAAI,CAACJ,SAAK,GAAI,CAAC;EAClC,IAAI,CAACA,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACP,IAAI,CAAC4F,WAAW,CAAC;AAC9B;AAEO,SAASO,WAAWA,CAAgBnG,IAAmB,EAAE;EAC9DoG,8BAA8B,CAAC,IAAI,EAAEpG,IAAI,EAAE,GAAG,CAAC;AACjD;AAEO,SAASqG,kBAAkBA,CAAgBrG,IAA0B,EAAE;EAC5EoG,8BAA8B,CAAC,IAAI,EAAEpG,IAAI,EAAE,GAAG,CAAC;AACjD;AAEA,SAASoG,8BAA8BA,CACrCvD,OAAgB,EAChB7C,IAA0C,EAC1CsG,GAAc,EACd;EAAA,IAAAC,iBAAA;EACA,IAAIC,eAAe,GAAG,CAAC;EACvB,KAAAD,iBAAA,GAAI1D,OAAO,CAACjC,QAAQ,aAAhB2F,iBAAA,CAAkBE,YAAY,CAACzG,IAAI,EAAEsG,GAAG,CAAC,EAAE;IAC7CE,eAAe,GAAG,CAAC;IACnB3D,OAAO,CAAC3C,KAAK,CAACoG,GAAG,CAAC;EACpB;EAEAzD,OAAO,CAAC4C,SAAS,CAACzF,IAAI,CAAC0G,KAAK,EAAEC,SAAS,EAAEA,SAAS,EAAE,UAAUC,CAAC,EAAE;IAC/D,IAAI,CAACvG,KAAK,CAAC,CAAC;IACZ,IAAI,CAACH,KAAK,CAACoG,GAAG,EAAE,IAAI,EAAEM,CAAC,GAAGJ,eAAe,CAAC;IAC1C,IAAI,CAACnG,KAAK,CAAC,CAAC;EACd,CAAC,CAAC;AACJ;AAEO,SAASwG,iBAAiBA,CAAgB7G,IAAyB,EAAE;EAC1E,IAAI,CAACO,KAAK,CAACP,IAAI,CAAC8G,SAAS,CAAC;EAC1B,IAAI,CAACzG,KAAK,CAAC,CAAC;EACZ,IAAI,CAACiB,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACP,IAAI,CAAC+G,WAAW,CAAC;EAC5B,IAAI,CAAC1G,KAAK,CAAC,CAAC;EACZ,IAAI,CAACH,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACP,IAAI,CAACgH,QAAQ,CAAC;EACzB,IAAI,CAAC3G,KAAK,CAAC,CAAC;EACZ,IAAI,CAACH,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACP,IAAI,CAACiH,SAAS,CAAC;AAC5B;AAEO,SAASC,WAAWA,CAAgBlH,IAAmB,EAAE;EAC9D,IAAI,CAACsB,IAAI,CAAC,OAAO,CAAC;EAClB,IAAI,CAACf,KAAK,CAACP,IAAI,CAACmH,aAAa,CAAC;AAChC;AAEO,SAASC,mBAAmBA,CAEjCpH,IAA2B,EAC3B;EACA,IAAI,CAACE,SAAK,GAAI,CAAC;EACf,IAAI,CAACK,KAAK,CAACP,IAAI,CAACI,cAAc,CAAC;EAC/B,IAAI,CAACF,SAAK,GAAI,CAAC;AACjB;AAEO,SAASmH,cAAcA,CAAgBrH,IAAsB,EAAE;EACpE,IAAI,CAACsB,IAAI,CAACtB,IAAI,CAACsH,QAAQ,CAAC;EACxB,IAAI,CAACjH,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACP,IAAI,CAACI,cAAc,CAAC;AACjC;AAEO,SAASmH,mBAAmBA,CAEjCvH,IAA2B,EAC3B;EACA,IAAI,CAACO,KAAK,CAACP,IAAI,CAACwH,UAAU,EAAE,IAAI,CAAC;EACjC,IAAI,CAACtH,SAAK,GAAI,CAAC;EACf,IAAI,CAACK,KAAK,CAACP,IAAI,CAACyH,SAAS,CAAC;EAC1B,IAAI,CAACvH,SAAK,GAAI,CAAC;AACjB;AAEO,SAASwH,YAAYA,CAAgB1H,IAAoB,EAAE;EAChE,MAAM;IAAE2H,QAAQ;IAAErH,QAAQ;IAAEwB,QAAQ;IAAE1B;EAAe,CAAC,GAAGJ,IAAI;EAC7D,IAAI,CAACE,SAAK,IAAI,CAAC;EACf,MAAM0H,IAAI,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;EAClC,IAAI,CAACxH,KAAK,CAAC,CAAC;EACZ,IAAIyB,QAAQ,EAAE;IACZgG,gBAAgB,CAAC,IAAI,EAAEhG,QAAQ,CAAC;IAChC,IAAI,CAACR,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACd;EAEA,IAAI,CAACH,SAAK,GAAI,CAAC;EAIR;IAEL,IAAI,CAACoB,IAAI,CAACtB,IAAI,CAACmH,aAAa,CAAC1F,IAAI,CAAC;EACpC;EAEA,IAAI,CAACpB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACiB,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACjB,KAAK,CAAC,CAAC;EAIL;IAEL,IAAI,CAACE,KAAK,CAACP,IAAI,CAACmH,aAAa,CAACzF,UAAU,CAAC;EAC3C;EAEA,IAAIiG,QAAQ,EAAE;IACZ,IAAI,CAACtH,KAAK,CAAC,CAAC;IACZ,IAAI,CAACiB,IAAI,CAAC,IAAI,CAAC;IACf,IAAI,CAACjB,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACoH,QAAQ,CAAC;EACtB;EAEA,IAAI,CAACzH,SAAK,GAAI,CAAC;EAEf,IAAII,QAAQ,EAAE;IACZwH,gBAAgB,CAAC,IAAI,EAAExH,QAAQ,CAAC;IAChC,IAAI,CAACJ,SAAK,GAAI,CAAC;EACjB;EAEA,IAAIE,cAAc,EAAE;IAClB,IAAI,CAACF,SAAK,GAAI,CAAC;IACf,IAAI,CAACG,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACH,cAAc,CAAC;EAC5B;EACA,IAAI,CAACC,KAAK,CAAC,CAAC;EACZuH,IAAI,CAAC,CAAC;EACN,IAAI,CAAC1H,SAAK,IAAI,CAAC;AACjB;AAEA,SAAS4H,gBAAgBA,CAACC,IAAa,EAAEC,GAAqB,EAAE;EAC9D,IAAIA,GAAG,KAAK,IAAI,EAAE;IAChBD,IAAI,CAAC7H,KAAK,CAAC8H,GAAG,CAAC;EACjB;AACF;AAEO,SAASC,qBAAqBA,CAEnCjI,IAA6B,EAC7B;EACA,IAAI,CAACkI,cAAc,CAAClI,IAAI,EAAEA,IAAI,CAAC0G,KAAK,CAAC;AACvC;AAEO,SAASyB,aAAaA,CAAgBnI,IAAqB,EAAE;EAClE,IAAI,CAACO,KAAK,CAACP,IAAI,CAACoI,OAAO,CAAC;AAC1B;AAEO,SAASC,iBAAiBA,CAG/BrI,IAGC,EACD;EACA,IAAI,CAACO,KAAK,CAACP,IAAI,CAACsI,UAAU,CAAC;EAC3B,IAAI,CAAC/H,KAAK,CAACP,IAAI,CAACgF,aAAa,CAAC;AAChC;AAIO,SAASuD,sBAAsBA,CAEpCvI,IAA8B,EAC9B;EACA,MAAM;IAAEkC,OAAO;IAAEsG,EAAE;IAAE3D,cAAc;IAAE4D,OAAO,EAAEC,OAAO;IAAEC;EAAK,CAAC,GAAG3I,IAAI;EACpE,IAAIkC,OAAO,EAAE;IACX,IAAI,CAACZ,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACiB,IAAI,CAAC,WAAW,CAAC;EACtB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACiI,EAAE,CAAC;EACd,IAAI,CAACjI,KAAK,CAACsE,cAAc,CAAC;EAC1B,IAAI6D,OAAO,YAAPA,OAAO,CAAE/H,MAAM,EAAE;IACnB,IAAI,CAACN,KAAK,CAAC,CAAC;IACZ,IAAI,CAACiB,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACjB,KAAK,CAAC,CAAC;IACZ,IAAI,CAACc,SAAS,CAACuH,OAAO,CAAC;EACzB;EACA,IAAI,CAACrI,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACoI,IAAI,CAAC;AAClB;AAEO,SAASC,eAAeA,CAAgB5I,IAAuB,EAAE;EACtEwF,WAAW,CAAC,IAAI,EAAExF,IAAI,EAAE,MAAM,IAAI,CAACyF,SAAS,CAACzF,IAAI,CAAC2I,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACtE;AAEO,SAASE,sBAAsBA,CAEpC7I,IAA8B,EAC9B;EACA,MAAM;IAAEkC,OAAO;IAAEsG,EAAE;IAAE3D,cAAc;IAAEzE;EAAe,CAAC,GAAGJ,IAAI;EAC5D,IAAIkC,OAAO,EAAE;IACX,IAAI,CAACZ,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACiB,IAAI,CAAC,MAAM,CAAC;EACjB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACiI,EAAE,CAAC;EACd,IAAI,CAACjI,KAAK,CAACsE,cAAc,CAAC;EAC1B,IAAI,CAACxE,KAAK,CAAC,CAAC;EACZ,IAAI,CAACH,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACH,cAAc,CAAC;EAC1B,IAAI,CAACgC,SAAS,CAAC,CAAC;AAClB;AAEA,SAAS0G,gBAAgBA,CAEvB9I,IAAgD,EAChD;EACA,MAAM;IAAEG,IAAI;IAAEmI,UAAU;IAAElI;EAAe,CAAC,GAAGJ,IAAI;EACjD,IAAI,CAACO,KAAK,CAAC+H,UAAU,EAAE,IAAI,CAAC;EAC5B,IAAI,CAACjI,KAAK,CAAC,CAAC;EACZ,IAAI,CAACiB,IAAI,CAACnB,IAAI,KAAK,gBAAgB,GAAG,IAAI,GAAG,WAAW,CAAC;EACzD,IAAI,CAACE,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACH,cAAc,CAAC;AAC5B;AAOO,SAAS2I,eAAeA,CAAgB/I,IAAuB,EAAE;EACtE,MAAM;IAAEI,cAAc;IAAEkI;EAAW,CAAC,GAAGtI,IAAI;EAC3C,IAAI,CAACE,SAAK,GAAI,CAAC;EACf,IAAI,CAACK,KAAK,CAACH,cAAc,CAAC;EAC1B,IAAI,CAACF,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAAC+H,UAAU,CAAC;AACxB;AAEO,SAASU,yBAAyBA,CAEvChJ,IAAiC,EACjC;EACA,IAAI,CAACO,KAAK,CAACP,IAAI,CAACsI,UAAU,CAAC;EAIpB;IAEL,IAAI,CAAC/H,KAAK,CAACP,IAAI,CAAC6E,cAAc,CAAC;EACjC;AACF;AAEO,SAASoE,iBAAiBA,CAAgBjJ,IAAyB,EAAE;EAC1E,MAAM;IAAEkC,OAAO;IAAEb,KAAK,EAAE6H,OAAO;IAAEV;EAAG,CAAC,GAAGxI,IAAI;EAC5C,IAAIkC,OAAO,EAAE;IACX,IAAI,CAACZ,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACd;EACA,IAAI6I,OAAO,EAAE;IACX,IAAI,CAAC5H,IAAI,CAAC,OAAO,CAAC;IAClB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACiB,IAAI,CAAC,MAAM,CAAC;EACjB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACiI,EAAE,CAAC;EACd,IAAI,CAACnI,KAAK,CAAC,CAAC;EAKL;IAEL8I,UAAU,CAACC,IAAI,CAAC,IAAI,EAAEpJ,IAA+B,CAAC;EACxD;AACF;AAEO,SAASmJ,UAAUA,CAAgBnJ,IAAkB,EAAE;EAC5DwF,WAAW,CAAC,IAAI,EAAExF,IAAI,EAAE;IAAA,IAAAqJ,qBAAA;IAAA,OACtB,IAAI,CAAClI,SAAS,CACZnB,IAAI,CAAC0F,OAAO,GAAA2D,qBAAA,GACZ,IAAI,CAACnI,wBAAwB,CAAC,GAAG,CAAC,YAAAmI,qBAAA,GACQ,IAAI,EAC9C,IAAI,EACJ,IACF,CAAC;EAAA,CACH,CAAC;AACH;AAEO,SAASC,YAAYA,CAAgBtJ,IAAoB,EAAE;EAChE,MAAM;IAAEwI,EAAE;IAAEe;EAAY,CAAC,GAAGvJ,IAAI;EAChC,IAAI,CAACO,KAAK,CAACiI,EAAE,CAAC;EACd,IAAIe,WAAW,EAAE;IACf,IAAI,CAAClJ,KAAK,CAAC,CAAC;IACZ,IAAI,CAACH,SAAK,GAAI,CAAC;IACf,IAAI,CAACG,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACgJ,WAAW,CAAC;EACzB;AACF;AAEO,SAASC,mBAAmBA,CAEjCxJ,IAA2B,EAC3B;EACA,MAAM;IAAEkC,OAAO;IAAEsG,EAAE;IAAEnF;EAAK,CAAC,GAAGrD,IAAI;EAElC,IAAIkC,OAAO,EAAE;IACX,IAAI,CAACZ,IAAI,CAAC,SAAS,CAAC;IACpB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACd;EAeO;IAEL,IAAI,CAACL,IAAI,CAACyJ,MAAM,EAAE;MAChB,IAAI,CAACnI,IAAI,CAAC+B,IAAI,WAAJA,IAAI,GAAKmF,EAAE,CAACrI,IAAI,KAAK,YAAY,GAAG,WAAW,GAAG,QAAS,CAAC;MACtE,IAAI,CAACE,KAAK,CAAC,CAAC;IACd;IAEA,IAAI,CAACE,KAAK,CAACiI,EAAE,CAAC;IAEd,IAAI,CAACxI,IAAI,CAAC2I,IAAI,EAAE;MACd,IAAI,CAACvG,SAAS,CAAC,CAAC;MAChB;IACF;IAEA,IAAIuG,IAAI,GAAG3I,IAAI,CAAC2I,IAAI;IAEpB,OAAOA,IAAI,CAACxI,IAAI,KAAK,qBAAqB,EAAE;MAC1C,IAAI,CAACD,SAAK,GAAI,CAAC;MAEf,IAAI,CAACK,KAAK,CAACoI,IAAI,CAACH,EAAE,CAAC;MAEnBG,IAAI,GAAGA,IAAI,CAACA,IAAI;IAClB;IAEA,IAAI,CAACtI,KAAK,CAAC,CAAC;IACZ,IAAI,CAACE,KAAK,CAACoI,IAAI,CAAC;EAClB;AACF;AAEO,SAASe,aAAaA,CAAgB1J,IAAqB,EAAE;EAClEwF,WAAW,CAAC,IAAI,EAAExF,IAAI,EAAE,MAAM,IAAI,CAAC2J,aAAa,CAAC3J,IAAI,CAAC2I,IAAI,EAAE,IAAI,CAAC,CAAC;AACpE;AAEO,SAASiB,YAAYA,CAAgB5J,IAAoB,EAAE;EAChE,MAAM;IAAE6J,QAAQ;IAAEC,SAAS;IAAEC;EAAQ,CAAC,GAAG/J,IAAI;EAC7C,IAAI,CAACsB,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACpB,SAAK,GAAI,CAAC;EACf,IAAI,CAACK,KAAK,CAACsJ,QAAQ,CAAC;EACpB,IAAIE,OAAO,EAAE;IACX,IAAI,CAAC7J,SAAK,GAAI,CAAC;IACf,IAAI,CAACK,KAAK,CAACwJ,OAAO,CAAC;EACrB;EACA,IAAI,CAAC7J,SAAK,GAAI,CAAC;EACf,IAAI4J,SAAS,EAAE;IACb,IAAI,CAAC5J,SAAK,GAAI,CAAC;IACf,IAAI,CAACK,KAAK,CAACuJ,SAAS,CAAC;EACvB;EACA,MAAM9E,aAAa,GAIfhF,IAAI,CAAC6E,cAAc;EACvB,IAAIG,aAAa,EAAE;IACjB,IAAI,CAACzE,KAAK,CAACyE,aAAa,CAAC;EAC3B;AACF;AAEO,SAASgF,yBAAyBA,CAEvChK,IAAiC,EACjC;EACA,MAAM;IAAEwI,EAAE;IAAEyB;EAAgB,CAAC,GAAGjK,IAAI;EACpC,IAGEA,IAAI,CAACkK,QAAQ,EACb;IACA,IAAI,CAAC5I,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACd;EACA,IAAI,CAACiB,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACiI,EAAE,CAAC;EACd,IAAI,CAACnI,KAAK,CAAC,CAAC;EACZ,IAAI,CAACH,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAAC0J,eAAe,CAAC;EAC3B,IAAI,CAAC7H,SAAS,CAAC,CAAC;AAClB;AAEO,SAAS+H,yBAAyBA,CAEvCnK,IAAiC,EACjC;EACA,IAAI,CAACE,KAAK,CAAC,UAAU,CAAC;EACtB,IAAI,CAACK,KAAK,CAACP,IAAI,CAACsI,UAAU,CAAC;EAC3B,IAAI,CAACpI,SAAK,GAAI,CAAC;AACjB;AAEO,SAASkK,mBAAmBA,CAEjCpK,IAA2B,EAC3B;EACA,IAAI,CAACO,KAAK,CAACP,IAAI,CAACsI,UAAU,CAAC;EAC3B,IAAI,CAACpI,SAAK,GAAI,CAAC;AACjB;AAEO,SAASmK,kBAAkBA,CAAgBrK,IAA0B,EAAE;EAC5E,IAAI,CAACsB,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACH,SAAK,GAAI,CAAC;EACf,IAAI,CAACG,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACP,IAAI,CAACsI,UAAU,CAAC;EAC3B,IAAI,CAAClG,SAAS,CAAC,CAAC;AAClB;AAEO,SAASkI,4BAA4BA,CAE1CtK,IAAoC,EACpC;EACA,IAAI,CAACsB,IAAI,CAAC,QAAQ,CAAC;EACnB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACiB,IAAI,CAAC,IAAI,CAAC;EACf,IAAI,CAACjB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACiB,IAAI,CAAC,WAAW,CAAC;EACtB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACZ,IAAI,CAACE,KAAK,CAACP,IAAI,CAACwI,EAAE,CAAC;EACnB,IAAI,CAACpG,SAAS,CAAC,CAAC;AAClB;AAEO,SAASO,+BAA+BA,CAAgB3C,IAAS,EAAE;EACxE,MAAM;IAAE6E;EAAe,CAAC,GAAG7E,IAAI;EAC/B,MAAM0D,UAAU,GAEZ1D,IAAI,CAAC0D,UAAU;EACnB,IAAI,CAACnD,KAAK,CAACsE,cAAc,CAAC;EAC1B,IAAI,CAAC3E,SAAK,GAAI,CAAC;EACf,IAAI,CAACuD,WAAW,CAACC,UAAU,EAAE,GAAG,CAAC;EACjC,MAAMoB,UAAU,GAEZ9E,IAAI,CAACI,cAAc;EACvB,IAAI,CAACG,KAAK,CAACuE,UAAU,CAAC;AACxB;AAEO,SAASyF,2BAA2BA,CAEzCvK,IAMqB,EACrB;EACA,MAAMwK,cAAc,GAAGxK,IAAI,CAACG,IAAI,KAAK,sBAAsB;EAC3D,MAAMsK,aAAa,GACjBzK,IAAI,CAACG,IAAI,KAAK,uBAAuB,IAAIH,IAAI,CAACG,IAAI,KAAK,eAAe;EACxEuK,kBAAkB,CAAC,IAAI,EAAE1K,IAAI,EAAE,CAC7ByK,aAAa,IAAIzK,IAAI,CAACkC,OAAO,IAAI,SAAS,EAC1C,CAACsI,cAAc,IAAIxK,IAAI,CAAC6B,aAAa,CACtC,CAAC;EACF,IAAI7B,IAAI,CAACuD,MAAM,EAAE;IACf,IAAI,CAACjC,IAAI,CAAC,QAAQ,CAAC;IACnB,IAAI,CAACjB,KAAK,CAAC,CAAC;EACd;EACAqK,kBAAkB,CAAC,IAAI,EAAE1K,IAAI,EAAE,CAC7B,CAACwK,cAAc,IAAIxK,IAAI,CAAC4E,QAAQ,IAAI,UAAU,EAC9C,CAAC4F,cAAc,IAAIxK,IAAI,CAAC2K,QAAQ,IAAI,UAAU,EAC9C,CAACF,aAAa,IAAID,cAAc,KAAKxK,IAAI,CAAC8B,QAAQ,IAAI,UAAU,CACjE,CAAC;AACJ;AAEA,SAAS0D,WAAWA,CAAC3C,OAAgB,EAAE7C,IAAY,EAAE4K,EAAc,EAAE;EACnE/H,OAAO,CAAC3C,KAAK,CAAC,GAAG,CAAC;EAClB,MAAM0H,IAAI,GAAG/E,OAAO,CAACgF,cAAc,CAAC,CAAC;EACrC+C,EAAE,CAAC,CAAC;EACJhD,IAAI,CAAC,CAAC;EACN/E,OAAO,CAACgI,UAAU,CAAC7K,IAAI,CAAC;AAC1B;AAEA,SAAS0K,kBAAkBA,CACzB7H,OAAgB,EAChB7C,IAAY,EACZ8K,SAAoC,EACpC;EAAA,IAAAC,kBAAA;EACA,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAAS,CAAC;EACtC,KAAK,MAAMC,QAAQ,IAAIJ,SAAS,EAAE;IAChC,IAAII,QAAQ,EAAEF,YAAY,CAACG,GAAG,CAACD,QAAQ,CAAC;EAC1C;EAEA,CAAAH,kBAAA,GAAAlI,OAAO,CAACjC,QAAQ,aAAhBmK,kBAAA,CAAkBhK,IAAI,CAACf,IAAI,EAAEgI,GAAG,IAAI;IAClC,IAAIgD,YAAY,CAACI,GAAG,CAACpD,GAAG,CAACqD,KAAK,CAAC,EAAE;MAC/BxI,OAAO,CAAC3C,KAAK,CAAC8H,GAAG,CAACqD,KAAK,CAAC;MACxBxI,OAAO,CAACxC,KAAK,CAAC,CAAC;MACf2K,YAAY,CAACM,MAAM,CAACtD,GAAG,CAACqD,KAAK,CAAC;MAC9B,OAAOL,YAAY,CAACO,IAAI,KAAK,CAAC;IAChC;EACF,CAAC,CAAC;EAEF,KAAK,MAAML,QAAQ,IAAIF,YAAY,EAAE;IACnCnI,OAAO,CAACvB,IAAI,CAAC4J,QAAQ,CAAC;IACtBrI,OAAO,CAACxC,KAAK,CAAC,CAAC;EACjB;AACF", "ignoreList": []}