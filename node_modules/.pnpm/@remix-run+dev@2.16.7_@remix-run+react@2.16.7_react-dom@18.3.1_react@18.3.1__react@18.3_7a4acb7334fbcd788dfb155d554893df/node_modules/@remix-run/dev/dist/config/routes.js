/**
 * @remix-run/dev v2.16.7
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var path = require('node:path');
var v = require('valibot');
var invariant = require('../invariant.js');

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n["default"] = e;
  return Object.freeze(n);
}

var path__namespace = /*#__PURE__*/_interopNamespace(path);
var v__namespace = /*#__PURE__*/_interopNamespace(v);

function setRouteConfigAppDirectory(directory) {
  globalThis.__remixAppDirectory = directory;
}

/**
 * Provides the absolute path to the app directory, for use within `routes.ts`.
 * This is designed to support resolving file system routes.
 */
function getRouteConfigAppDirectory() {
  invariant["default"](globalThis.__remixAppDirectory);
  return globalThis.__remixAppDirectory;
}
function routeManifestToRouteConfig(routeManifest, rootId = "root") {
  let routeConfigById = {};
  for (let id in routeManifest) {
    let route = routeManifest[id];
    routeConfigById[id] = {
      id: route.id,
      file: route.file,
      path: route.path,
      index: route.index,
      caseSensitive: route.caseSensitive
    };
  }
  let routeConfig = [];
  for (let id in routeConfigById) {
    let route = routeConfigById[id];
    let parentId = routeManifest[route.id].parentId;
    if (parentId === rootId) {
      routeConfig.push(route);
    } else {
      let parentRoute = parentId && routeConfigById[parentId];
      if (parentRoute) {
        parentRoute.children = parentRoute.children || [];
        parentRoute.children.push(route);
      }
    }
  }
  return routeConfig;
}

/**
 * Configuration for an individual route, for use within `routes.ts`. As a
 * convenience, route config entries can be created with the {@link route},
 * {@link index} and {@link layout} helper functions.
 */

const routeConfigEntrySchema = v__namespace.pipe(v__namespace.custom(value => {
  return !(typeof value === "object" && value !== null && "then" in value && "catch" in value);
}, "Invalid type: Expected object but received a promise. Did you forget to await?"), v__namespace.object({
  id: v__namespace.optional(v__namespace.string()),
  path: v__namespace.optional(v__namespace.string()),
  index: v__namespace.optional(v__namespace.boolean()),
  caseSensitive: v__namespace.optional(v__namespace.boolean()),
  file: v__namespace.string(),
  children: v__namespace.optional(v__namespace.array(v__namespace.lazy(() => routeConfigEntrySchema)))
}));
const resolvedRouteConfigSchema = v__namespace.array(routeConfigEntrySchema);

/**
 * Route config to be exported via the `routes` export within `routes.ts`.
 */

function validateRouteConfig({
  routeConfigFile,
  routeConfig
}) {
  if (!routeConfig) {
    return {
      valid: false,
      message: `Route config must be the default export in "${routeConfigFile}".`
    };
  }
  if (!Array.isArray(routeConfig)) {
    return {
      valid: false,
      message: `Route config in "${routeConfigFile}" must be an array.`
    };
  }
  let {
    issues
  } = v__namespace.safeParse(resolvedRouteConfigSchema, routeConfig);
  if (issues !== null && issues !== void 0 && issues.length) {
    let {
      root,
      nested
    } = v__namespace.flatten(issues);
    return {
      valid: false,
      message: [`Route config in "${routeConfigFile}" is invalid.`, root ? `${root}` : [], nested ? Object.entries(nested).map(([path, message]) => `Path: routes.${path}\n${message}`) : []].flat().join("\n\n")
    };
  }
  return {
    valid: true
  };
}
function configRoutesToRouteManifest(routes, rootId = "root") {
  let routeManifest = {};
  function walk(route, parentId) {
    let id = route.id || createRouteId(route.file);
    let manifestItem = {
      id,
      parentId,
      file: route.file,
      path: route.path,
      index: route.index,
      caseSensitive: route.caseSensitive
    };
    if (routeManifest.hasOwnProperty(id)) {
      throw new Error(`Unable to define routes with duplicate route id: "${id}"`);
    }
    routeManifest[id] = manifestItem;
    if (route.children) {
      for (let child of route.children) {
        walk(child, id);
      }
    }
  }
  for (let route of routes) {
    walk(route, rootId);
  }
  return routeManifest;
}

/**
 * A function for defining a route that is passed as the argument to the
 * `defineRoutes` callback.
 *
 * Calls to this function are designed to be nested, using the `children`
 * callback argument.
 *
 *   defineRoutes(route => {
 *     route('/', 'pages/layout', () => {
 *       route('react-router', 'pages/react-router');
 *       route('reach-ui', 'pages/reach-ui');
 *     });
 *   });
 */

/**
 * A function for defining routes programmatically, instead of using the
 * filesystem convention.
 */
function defineRoutes(callback) {
  let routes = Object.create(null);
  let parentRoutes = [];
  let alreadyReturned = false;
  let defineRoute = (path, file, optionsOrChildren, children) => {
    if (alreadyReturned) {
      throw new Error("You tried to define routes asynchronously but started defining " + "routes before the async work was done. Please await all async " + "data before calling `defineRoutes()`");
    }
    let options;
    if (typeof optionsOrChildren === "function") {
      // route(path, file, children)
      options = {};
      children = optionsOrChildren;
    } else {
      // route(path, file, options, children)
      // route(path, file, options)
      options = optionsOrChildren || {};
    }
    let route = {
      path: path ? path : undefined,
      index: options.index ? true : undefined,
      caseSensitive: options.caseSensitive ? true : undefined,
      id: options.id || createRouteId(file),
      parentId: parentRoutes.length > 0 ? parentRoutes[parentRoutes.length - 1].id : "root",
      file
    };
    if (route.id in routes) {
      throw new Error(`Unable to define routes with duplicate route id: "${route.id}"`);
    }
    routes[route.id] = route;
    if (children) {
      parentRoutes.push(route);
      children();
      parentRoutes.pop();
    }
  };
  callback(defineRoute);
  alreadyReturned = true;
  return routes;
}
function createRouteId(file) {
  return normalizeSlashes(stripFileExtension(file));
}
function normalizeSlashes(file) {
  return file.split(path__namespace.win32.sep).join("/");
}
function stripFileExtension(file) {
  return file.replace(/\.[a-z0-9]+$/i, "");
}

exports.configRoutesToRouteManifest = configRoutesToRouteManifest;
exports.createRouteId = createRouteId;
exports.defineRoutes = defineRoutes;
exports.getRouteConfigAppDirectory = getRouteConfigAppDirectory;
exports.normalizeSlashes = normalizeSlashes;
exports.resolvedRouteConfigSchema = resolvedRouteConfigSchema;
exports.routeConfigEntrySchema = routeConfigEntrySchema;
exports.routeManifestToRouteConfig = routeManifestToRouteConfig;
exports.setRouteConfigAppDirectory = setRouteConfigAppDirectory;
exports.validateRouteConfig = validateRouteConfig;
