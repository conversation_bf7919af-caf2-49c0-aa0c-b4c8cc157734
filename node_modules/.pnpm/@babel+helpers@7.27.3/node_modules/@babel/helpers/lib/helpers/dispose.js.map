{"version": 3, "names": ["dispose_SuppressedError", "error", "suppressed", "SuppressedError", "stack", "Error", "prototype", "Object", "create", "constructor", "value", "writable", "configurable", "_dispose", "<PERSON><PERSON><PERSON><PERSON>", "next", "length", "r", "pop", "p", "d", "call", "v", "a", "Promise", "resolve", "then", "err", "e"], "sources": ["../../src/helpers/dispose.js"], "sourcesContent": ["/* @minVersion 7.22.0 */\n/* @onlyBabel7 */\n\nfunction dispose_SuppressedError(error, suppressed) {\n  if (typeof SuppressedError !== \"undefined\") {\n    // eslint-disable-next-line no-undef\n    dispose_SuppressedError = SuppressedError;\n  } else {\n    dispose_SuppressedError = function SuppressedError(error, suppressed) {\n      this.suppressed = suppressed;\n      this.error = error;\n      this.stack = new Error().stack;\n    };\n    dispose_SuppressedError.prototype = Object.create(Error.prototype, {\n      constructor: {\n        value: dispose_SuppressedError,\n        writable: true,\n        configurable: true,\n      },\n    });\n  }\n  return new dispose_SuppressedError(error, suppressed);\n}\n\nexport default function _dispose(stack, error, hasError) {\n  function next() {\n    while (stack.length > 0) {\n      try {\n        var r = stack.pop();\n        var p = r.d.call(r.v);\n        if (r.a) return Promise.resolve(p).then(next, err);\n      } catch (e) {\n        return err(e);\n      }\n    }\n    if (hasError) throw error;\n  }\n\n  function err(e) {\n    error = hasError ? new dispose_SuppressedError(error, e) : e;\n    hasError = true;\n\n    return next();\n  }\n\n  return next();\n}\n"], "mappings": ";;;;;;AAGA,SAASA,uBAAuBA,CAACC,KAAK,EAAEC,UAAU,EAAE;EAClD,IAAI,OAAOC,eAAe,KAAK,WAAW,EAAE;IAE1CH,uBAAuB,GAAGG,eAAe;EAC3C,CAAC,MAAM;IACLH,uBAAuB,GAAG,SAASG,eAAeA,CAACF,KAAK,EAAEC,UAAU,EAAE;MACpE,IAAI,CAACA,UAAU,GAAGA,UAAU;MAC5B,IAAI,CAACD,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACG,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC,CAACD,KAAK;IAChC,CAAC;IACDJ,uBAAuB,CAACM,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACH,KAAK,CAACC,SAAS,EAAE;MACjEG,WAAW,EAAE;QACXC,KAAK,EAAEV,uBAAuB;QAC9BW,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE;MAChB;IACF,CAAC,CAAC;EACJ;EACA,OAAO,IAAIZ,uBAAuB,CAACC,KAAK,EAAEC,UAAU,CAAC;AACvD;AAEe,SAASW,QAAQA,CAACT,KAAK,EAAEH,KAAK,EAAEa,QAAQ,EAAE;EACvD,SAASC,IAAIA,CAAA,EAAG;IACd,OAAOX,KAAK,CAACY,MAAM,GAAG,CAAC,EAAE;MACvB,IAAI;QACF,IAAIC,CAAC,GAAGb,KAAK,CAACc,GAAG,CAAC,CAAC;QACnB,IAAIC,CAAC,GAAGF,CAAC,CAACG,CAAC,CAACC,IAAI,CAACJ,CAAC,CAACK,CAAC,CAAC;QACrB,IAAIL,CAAC,CAACM,CAAC,EAAE,OAAOC,OAAO,CAACC,OAAO,CAACN,CAAC,CAAC,CAACO,IAAI,CAACX,IAAI,EAAEY,GAAG,CAAC;MACpD,CAAC,CAAC,OAAOC,CAAC,EAAE;QACV,OAAOD,GAAG,CAACC,CAAC,CAAC;MACf;IACF;IACA,IAAId,QAAQ,EAAE,MAAMb,KAAK;EAC3B;EAEA,SAAS0B,GAAGA,CAACC,CAAC,EAAE;IACd3B,KAAK,GAAGa,QAAQ,GAAG,IAAId,uBAAuB,CAACC,KAAK,EAAE2B,CAAC,CAAC,GAAGA,CAAC;IAC5Dd,QAAQ,GAAG,IAAI;IAEf,OAAOC,IAAI,CAAC,CAAC;EACf;EAEA,OAAOA,IAAI,CAAC,CAAC;AACf", "ignoreList": []}